<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="GradleMigrationSettings" migrationVersion="1" />
  <component name="GradleSettings">
    <option name="linkedExternalProjectsSettings">
      <GradleProjectSettings>
        <option name="testRunner" value="CHOOSE_PER_TEST" />
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="gradleJvm" value="#GRADLE_LOCAL_JAVA_HOME" />
        <option name="modules">
          <set>
            <option value="$PROJECT_DIR$/../YYKM/tbsx5" />
            <option value="$PROJECT_DIR$" />
            <option value="$PROJECT_DIR$/core" />
            <option value="$PROJECT_DIR$/core/data" />
            <option value="$PROJECT_DIR$/core/designsystem" />
            <option value="$PROJECT_DIR$/core/util" />
            <option value="$PROJECT_DIR$/mobile" />
            <option value="$PROJECT_DIR$/tv" />
          </set>
        </option>
      </GradleProjectSettings>
    </option>
  </component>
</project>