<changelist name="Changes" date="1755505783065" recycled="false">
  <option name="PATH" value="$PROJECT_DIR$/.idea/shelf/Changes/shelved.patch" />
  <option name="DESCRIPTION" value="Changes" />
  <binary>
    <option name="BEFORE_PATH" value="allinone/src/main/assets/ca-bundle.crt" />
  </binary>
  <binary>
    <option name="BEFORE_PATH" value="allinone/jniLibs/arm64-v8a/libloader.so" />
  </binary>
  <binary>
    <option name="BEFORE_PATH" value="allinone/jniLibs/armeabi-v7a/libloader.so" />
  </binary>
  <binary>
    <option name="BEFORE_PATH" value="allinone/jniLibs/arm64-v8a/libproot.so" />
  </binary>
  <binary>
    <option name="BEFORE_PATH" value="allinone/jniLibs/armeabi-v7a/libproot.so" />
  </binary>
  <binary>
    <option name="BEFORE_PATH" value="allinone/jniLibs/arm64-v8a/libtermux-exec.so" />
  </binary>
  <binary>
    <option name="BEFORE_PATH" value="allinone/jniLibs/armeabi-v7a/libtermux-exec.so" />
  </binary>
  <binary>
    <option name="BEFORE_PATH" value="allinone/src/main/assets/resolv.conf" />
  </binary>
</changelist>