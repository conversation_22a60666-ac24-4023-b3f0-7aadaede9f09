Index: tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/channelline/components/ChannelLineItem.kt
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package top.yogiczy.mytv.tv.ui.screensold.channelline.components\r\n\r\nimport androidx.compose.foundation.layout.Arrangement\r\nimport androidx.compose.foundation.layout.Column\r\nimport androidx.compose.foundation.layout.Row\r\nimport androidx.compose.foundation.layout.padding\r\nimport androidx.compose.runtime.Composable\r\nimport androidx.compose.runtime.LaunchedEffect\r\nimport androidx.compose.runtime.getValue\r\nimport androidx.compose.runtime.mutableLongStateOf\r\nimport androidx.compose.runtime.mutableStateOf\r\nimport androidx.compose.runtime.remember\r\nimport androidx.compose.runtime.setValue\r\nimport androidx.compose.ui.Alignment\r\nimport androidx.compose.ui.Modifier\r\nimport androidx.compose.ui.text.style.TextOverflow\r\nimport androidx.compose.ui.tooling.preview.Preview\r\nimport androidx.compose.ui.unit.dp\r\nimport androidx.tv.material3.ListItem\r\nimport androidx.tv.material3.MaterialTheme\r\nimport androidx.tv.material3.RadioButton\r\nimport androidx.tv.material3.Text\r\nimport kotlinx.coroutines.Dispatchers\r\nimport kotlinx.coroutines.withContext\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelLine\r\nimport top.yogiczy.mytv.core.data.network.request\r\nimport top.yogiczy.mytv.core.data.utils.ChannelUtil\r\nimport top.yogiczy.mytv.core.util.utils.isIPv6\r\nimport top.yogiczy.mytv.tv.ui.material.Tag\r\nimport top.yogiczy.mytv.tv.ui.material.TagDefaults\r\nimport top.yogiczy.mytv.tv.ui.theme.MyTvTheme\r\nimport top.yogiczy.mytv.tv.ui.utils.focusOnLaunchedSaveable\r\nimport top.yogiczy.mytv.tv.ui.utils.handleKeyEvents\r\nimport top.yogiczy.mytv.tv.ui.utils.ifElse\r\nimport java.io.IOException\r\nimport kotlin.system.measureTimeMillis\r\n\r\n@Composable\r\nfun ChannelLineItem(\r\n    modifier: Modifier = Modifier,\r\n    lineProvider: () -> ChannelLine = { ChannelLine() },\r\n    lineIdxProvider: () -> Int = { 0 },\r\n    isSelectedProvider: () -> Boolean = { false },\r\n    onSelected: () -> Unit = {},\r\n) {\r\n    val line = lineProvider()\r\n    val lineIdx = lineIdxProvider()\r\n    val isSelected = isSelectedProvider()\r\n\r\n    val lineDelay = rememberLineDelay(line)\r\n\r\n    ListItem(\r\n        modifier = modifier\r\n            .ifElse(isSelected, Modifier.focusOnLaunchedSaveable())\r\n            .handleKeyEvents(onSelect = onSelected),\r\n        selected = false,\r\n        onClick = {},\r\n        headlineContent = { Text(line.name ?: \"线路${lineIdx + 1}\", maxLines = 1) },\r\n        overlineContent = {\r\n            Row(\r\n                horizontalArrangement = Arrangement.spacedBy(4.dp),\r\n                verticalAlignment = Alignment.CenterVertically,\r\n            ) {\r\n                if (line.url.startsWith(\"webview://\")) {\r\n                    Tag(\"混合\")\r\n                    Tag(ChannelUtil.getHybridWebViewUrlProvider(line.url))\r\n                } else {\r\n                    Tag(if (line.url.isIPv6()) \"IPv6\" else \"IPv4\")\r\n\r\n                    if (ChannelUtil.urlSupportPlayback(line.url)) Tag(\"回放\")\r\n\r\n                    if (lineDelay > 0L) {\r\n                        if (lineDelay < 500) {\r\n                            Tag(\r\n                                \"$lineDelay ms\",\r\n                                colors = TagDefaults.colors(containerColor = MaterialTheme.colorScheme.tertiary),\r\n                            )\r\n                        } else {\r\n                            Tag(\"$lineDelay ms\")\r\n                        }\r\n                    } else if (lineDelay < 0L) {\r\n                        Tag(\r\n                            \"超时\",\r\n                            colors = TagDefaults.colors(containerColor = MaterialTheme.colorScheme.error),\r\n                        )\r\n                    }\r\n                }\r\n            }\r\n        },\r\n        supportingContent = { Text(line.url, maxLines = 1, overflow = TextOverflow.Ellipsis) },\r\n        trailingContent = {\r\n            RadioButton(selected = isSelected, onClick = {})\r\n        },\r\n    )\r\n}\r\n\r\n@Composable\r\nprivate fun rememberLineDelay(line: ChannelLine): Long {\r\n    var elapsedTime by remember { mutableLongStateOf(0) }\r\n    var hasError by remember { mutableStateOf(false) }\r\n\r\n    LaunchedEffect(Unit) {\r\n        runCatching {\r\n            withContext(Dispatchers.IO) {\r\n                elapsedTime = measureTimeMillis {\r\n                    try {\r\n                        line.url.request({ builder ->\r\n                            builder\r\n                                .apply {\r\n                                    line.httpUserAgent?.let { header(\"User-Agent\", it) }\r\n                                }\r\n                        }) { body -> body.string() }\r\n                    } catch (_: IOException) {\r\n                        hasError = true\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    return if (hasError) -1 else elapsedTime\r\n}\r\n\r\n@Preview\r\n@Composable\r\nprivate fun ChannelLineItemPreview() {\r\n    MyTvTheme {\r\n        Column(\r\n            modifier = Modifier.padding(20.dp),\r\n            verticalArrangement = Arrangement.spacedBy(20.dp),\r\n        ) {\r\n            ChannelLineItem(\r\n                lineProvider = { ChannelLine.EXAMPLE },\r\n                lineIdxProvider = { 0 },\r\n                isSelectedProvider = { true },\r\n            )\r\n\r\n            ChannelLineItem(\r\n                lineProvider = { ChannelLine(\"http://[2409:8087:5e01:34::20]:6610/ZTE_CMS/00000001000000060000000000000131/index.m3u8?IAS\") },\r\n                lineIdxProvider = { 0 },\r\n            )\r\n\r\n            ChannelLineItem(\r\n                lineProvider = {\r\n                    ChannelLine(\r\n                        url = \"webview://https://tv.cctv.com/live/cctv1/\",\r\n                    )\r\n                },\r\n                lineIdxProvider = { 0 },\r\n                isSelectedProvider = { true },\r\n            )\r\n        }\r\n    }\r\n}
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/channelline/components/ChannelLineItem.kt b/tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/channelline/components/ChannelLineItem.kt
--- a/tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/channelline/components/ChannelLineItem.kt	(revision ****************************************)
+++ b/tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/channelline/components/ChannelLineItem.kt	(date 1755504805264)
@@ -67,7 +67,7 @@
                 } else {
                     Tag(if (line.url.isIPv6()) "IPv6" else "IPv4")
 
-                    if (ChannelUtil.urlSupportPlayback(line.url)) Tag("回放")
+                    if (ChannelUtil.channelLineSupportPlayback(line)) Tag("回放")
 
                     if (lineDelay > 0L) {
                         if (lineDelay < 500) {
Index: core/data/src/main/java/top/yogiczy/mytv/core/data/utils/ChannelUtil.kt
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package top.yogiczy.mytv.core.data.utils\r\n\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelLine\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelLineList\r\n\r\n\r\nobject ChannelUtil {\r\n    private val hybridWebViewUrl by lazy {\r\n        mapOf(\r\n            ChannelAlias.standardChannelName(\"cctv-1\") to listOf(\r\n                \"https://tv.cctv.com/live/cctv1/\",\r\n                \"https://yangshipin.cn/tv/home?pid=600001859\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cctv-2\") to listOf(\r\n                \"https://tv.cctv.com/live/cctv2/\",\r\n                \"https://yangshipin.cn/tv/home?pid=600001800\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cctv-3\") to listOf(\r\n                \"https://tv.cctv.com/live/cctv3/\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cctv-4\") to listOf(\r\n                \"https://tv.cctv.com/live/cctv4/\",\r\n                \"https://yangshipin.cn/tv/home?pid=600001814\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cctv-5\") to listOf(\r\n                \"https://tv.cctv.com/live/cctv5/\",\r\n                \"https://yangshipin.cn/tv/home?pid=600001818\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cctv-5+\") to listOf(\r\n                \"https://tv.cctv.com/live/cctv5plus/\",\r\n                \"https://yangshipin.cn/tv/home?pid=600001817\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cctv6\") to listOf(\r\n                \"https://tv.cctv.com/live/cctv6/\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cctv-7\") to listOf(\r\n                \"https://tv.cctv.com/live/cctv7/\",\r\n                \"https://yangshipin.cn/tv/home?pid=600004092\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cctv-8\") to listOf(\r\n                \"https://tv.cctv.com/live/cctv8/\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cctv-9\") to listOf(\r\n                \"https://tv.cctv.com/live/cctvjilu/\",\r\n                \"https://yangshipin.cn/tv/home?pid=600004078\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cctv-10\") to listOf(\r\n                \"https://tv.cctv.com/live/cctv10/\",\r\n                \"https://yangshipin.cn/tv/home?pid=600001805\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cctv-11\") to listOf(\r\n                \"https://tv.cctv.com/live/cctv11/\",\r\n                \"https://yangshipin.cn/tv/home?pid=600001806\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cctv-12\") to listOf(\r\n                \"https://tv.cctv.com/live/cctv12/\",\r\n                \"https://yangshipin.cn/tv/home?pid=600001807\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cctv-13\") to listOf(\r\n                \"https://tv.cctv.com/live/cctv13/\",\r\n                \"https://yangshipin.cn/tv/home?pid=600001811\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cctv-14\") to listOf(\r\n                \"https://tv.cctv.com/live/cctvchild/\",\r\n                \"https://yangshipin.cn/tv/home?pid=600001809\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cctv-15\") to listOf(\r\n                \"https://tv.cctv.com/live/cctv15/\",\r\n                \"https://yangshipin.cn/tv/home?pid=600001815\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cctv-16\") to listOf(\r\n                \"https://tv.cctv.com/live/cctv16/\",\r\n                \"https://yangshipin.cn/tv/home?pid=600098637\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"cctv-17\") to listOf(\r\n                \"https://tv.cctv.com/live/cctv17/\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"北京卫视\") to listOf(\r\n                \"https://yangshipin.cn/tv/home?pid=600002309\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"江苏卫视\") to listOf(\r\n                \"https://yangshipin.cn/tv/home?pid=600002521\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"上海卫视\") to listOf(\r\n                \"https://yangshipin.cn/tv/home?pid=600002483\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"浙江卫视\") to listOf(\r\n                \"https://yangshipin.cn/tv/home?pid=600002520\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"湖南卫视\") to listOf(\r\n                \"https://yangshipin.cn/tv/home?pid=600002475\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"湖北卫视\") to listOf(\r\n                \"https://yangshipin.cn/tv/home?pid=600002508\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"广东卫视\") to listOf(\r\n                \"https://yangshipin.cn/tv/home?pid=600002485\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"广西卫视\") to listOf(\r\n                \"https://yangshipin.cn/tv/home?pid=600002509\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"黑龙江卫视\") to listOf(\r\n                \"https://yangshipin.cn/tv/home?pid=600002498\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"海南卫视\") to listOf(\r\n                \"https://yangshipin.cn/tv/home?pid=600002506\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"重庆卫视\") to listOf(\r\n                \"https://yangshipin.cn/tv/home?pid=600002531\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"深圳卫视\") to listOf(\r\n                \"https://yangshipin.cn/tv/home?pid=600002481\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"四川卫视\") to listOf(\r\n                \"https://yangshipin.cn/tv/home?pid=600002516\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"河南卫视\") to listOf(\r\n                \"https://yangshipin.cn/tv/home?pid=600002525\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"福建卫视\") to listOf(\r\n                \"https://yangshipin.cn/tv/home?pid=600002484\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"贵州卫视\") to listOf(\r\n                \"https://yangshipin.cn/tv/home?pid=600002490\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"江西卫视\") to listOf(\r\n                \"https://yangshipin.cn/tv/home?pid=600002503\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"辽宁卫视\") to listOf(\r\n                \"https://yangshipin.cn/tv/home?pid=600002505\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"安徽卫视\") to listOf(\r\n                \"https://yangshipin.cn/tv/home?pid=600002532\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"河北卫视\") to listOf(\r\n                \"https://yangshipin.cn/tv/home?pid=600002493\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"山东卫视\") to listOf(\r\n                \"https://yangshipin.cn/tv/home?pid=600002513\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"天津卫视\") to listOf(\r\n                \"https://yangshipin.cn/tv/home?pid=600152137\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"吉林卫视\") to listOf(\r\n                \"https://yangshipin.cn/tv/home?pid=600190405\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"陕西卫视\") to listOf(\r\n                \"https://yangshipin.cn/tv/home?pid=600190400\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"甘肃卫视\") to listOf(\r\n                \"https://yangshipin.cn/tv/home?pid=600190408\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"宁夏卫视\") to listOf(\r\n                \"https://yangshipin.cn/tv/home?pid=600190737\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"内蒙古卫视\") to listOf(\r\n                \"https://yangshipin.cn/tv/home?pid=600190401\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"云南卫视\") to listOf(\r\n                \"https://yangshipin.cn/tv/home?pid=600190402\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"山西卫视\") to listOf(\r\n                \"https://yangshipin.cn/tv/home?pid=600190407\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"青海卫视\") to listOf(\r\n                \"https://yangshipin.cn/tv/home?pid=600190406\",\r\n            ),\r\n            ChannelAlias.standardChannelName(\"西藏卫视\") to listOf(\r\n                \"https://yangshipin.cn/tv/home?pid=600190403\",\r\n            ),\r\n        )\r\n    }\r\n\r\n    fun getHybridWebViewLines(channelName: String): ChannelLineList {\r\n        return ChannelLineList(hybridWebViewUrl[ChannelAlias.standardChannelName(channelName)]\r\n            ?.map { ChannelLine(url = \"webview://$it\") }\r\n            ?: emptyList())\r\n    }\r\n\r\n    fun getHybridWebViewUrlProvider(url: String): String {\r\n        return if (url.contains(\"https://tv.cctv.com\")) \"央视网\"\r\n        else if (url.contains(\"https://yangshipin.cn\")) \"央视频\"\r\n        else \"未知\"\r\n    }\r\n\r\n    fun urlSupportPlayback(url: String): Boolean {\r\n        return listOf(\"pltv\", \"tvod\").any { url.contains(it, ignoreCase = true) }\r\n    }\r\n\r\n    fun urlToCanPlayback(url: String): String {\r\n        return url.replace(\"pltv\", \"tvod\", ignoreCase = true)\r\n    }\r\n}
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/core/data/src/main/java/top/yogiczy/mytv/core/data/utils/ChannelUtil.kt b/core/data/src/main/java/top/yogiczy/mytv/core/data/utils/ChannelUtil.kt
--- a/core/data/src/main/java/top/yogiczy/mytv/core/data/utils/ChannelUtil.kt	(revision ****************************************)
+++ b/core/data/src/main/java/top/yogiczy/mytv/core/data/utils/ChannelUtil.kt	(date 1755505668677)
@@ -190,4 +190,20 @@
     fun urlToCanPlayback(url: String): String {
         return url.replace("pltv", "tvod", ignoreCase = true)
     }
+
+    fun channelLineSupportPlayback(channelLine: ChannelLine): Boolean {
+        return urlSupportPlayback(channelLine.url) || !channelLine.catchupSource.isNullOrBlank()
+    }
+
+    fun buildCatchupUrl(catchupSource: String, startTime: Long, endTime: Long): String {
+        val utcTimeFormat = java.text.SimpleDateFormat("yyyyMMddHHmmss", java.util.Locale.getDefault()).apply {
+            timeZone = java.util.TimeZone.getTimeZone("UTC")
+        }
+        val startTimeStr = utcTimeFormat.format(startTime)
+        val endTimeStr = utcTimeFormat.format(endTime)
+
+        return catchupSource
+            .replace("{utc:YmdHMS}", startTimeStr)
+            .replace("{utcend:YmdHMS}", endTimeStr)
+    }
 }
\ No newline at end of file
Index: tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/main/components/MainContentState.kt
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package top.yogiczy.mytv.tv.ui.screensold.main.components\r\n\r\nimport androidx.compose.runtime.Composable\r\nimport androidx.compose.runtime.Stable\r\nimport androidx.compose.runtime.getValue\r\nimport androidx.compose.runtime.mutableIntStateOf\r\nimport androidx.compose.runtime.mutableStateOf\r\nimport androidx.compose.runtime.remember\r\nimport androidx.compose.runtime.rememberCoroutineScope\r\nimport androidx.compose.runtime.rememberUpdatedState\r\nimport androidx.compose.runtime.setValue\r\nimport kotlinx.coroutines.CoroutineScope\r\nimport kotlinx.coroutines.Job\r\nimport kotlinx.coroutines.delay\r\nimport kotlinx.coroutines.launch\r\nimport top.yogiczy.mytv.core.data.entities.channel.Channel\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelGroupList\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelGroupList.Companion.channelFirstOrNull\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelGroupList.Companion.channelGroupIdx\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelGroupList.Companion.channelIdx\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelGroupList.Companion.channelLastOrNull\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelGroupList.Companion.channelList\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelLine\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelLineList\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelList\r\nimport top.yogiczy.mytv.core.data.entities.epg.EpgProgramme\r\nimport top.yogiczy.mytv.core.data.entities.epg.EpgProgrammeReserve\r\nimport top.yogiczy.mytv.core.data.entities.epg.EpgProgrammeReserveList\r\nimport top.yogiczy.mytv.core.data.utils.ChannelUtil\r\nimport top.yogiczy.mytv.core.data.utils.Constants\r\nimport top.yogiczy.mytv.core.data.utils.Loggable\r\nimport top.yogiczy.mytv.core.util.utils.urlHost\r\nimport top.yogiczy.mytv.tv.ui.material.Snackbar\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.SettingsViewModel\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.settingsVM\r\nimport top.yogiczy.mytv.tv.ui.screensold.videoplayer.VideoPlayerState\r\nimport top.yogiczy.mytv.tv.ui.screensold.videoplayer.player.VideoPlayer\r\nimport top.yogiczy.mytv.tv.ui.screensold.videoplayer.rememberVideoPlayerState\r\nimport java.net.URI\r\nimport java.text.SimpleDateFormat\r\nimport java.util.Locale\r\nimport kotlin.math.max\r\nimport kotlin.math.min\r\n\r\n@Stable\r\nclass MainContentState(\r\n    private val coroutineScope: CoroutineScope,\r\n    private val videoPlayerState: VideoPlayerState,\r\n    private val channelGroupListProvider: () -> ChannelGroupList = { ChannelGroupList() },\r\n    private val favoriteChannelListProvider: () -> ChannelList = { ChannelList() },\r\n    private val settingsViewModel: SettingsViewModel,\r\n) : Loggable(\"MainContentState\") {\r\n    private var _currentChannel by mutableStateOf(Channel())\r\n    val currentChannel get() = _currentChannel\r\n\r\n    private var _currentChannelLineIdx by mutableIntStateOf(0)\r\n    val currentChannelLineIdx get() = _currentChannelLineIdx\r\n\r\n    val currentChannelLine get() = _currentChannel.lineList[_currentChannelLineIdx]\r\n\r\n    private var _currentPlaybackEpgProgramme by mutableStateOf<EpgProgramme?>(null)\r\n    val currentPlaybackEpgProgramme get() = _currentPlaybackEpgProgramme\r\n\r\n    private var _tempChannelScreenHideJob: Job? = null\r\n\r\n    private var _isTempChannelScreenVisible by mutableStateOf(false)\r\n    var isTempChannelScreenVisible\r\n        get() = _isTempChannelScreenVisible\r\n        set(value) {\r\n            _isTempChannelScreenVisible = value\r\n        }\r\n\r\n    private var _isChannelScreenVisible by mutableStateOf(false)\r\n    var isChannelScreenVisible\r\n        get() = _isChannelScreenVisible\r\n        set(value) {\r\n            _isChannelScreenVisible = value\r\n        }\r\n\r\n    private var _isVideoPlayerControllerScreenVisible by mutableStateOf(false)\r\n    var isVideoPlayerControllerScreenVisible\r\n        get() = _isVideoPlayerControllerScreenVisible\r\n        set(value) {\r\n            _isVideoPlayerControllerScreenVisible = value\r\n        }\r\n\r\n    private var _isQuickOpScreenVisible by mutableStateOf(false)\r\n    var isQuickOpScreenVisible\r\n        get() = _isQuickOpScreenVisible\r\n        set(value) {\r\n            _isQuickOpScreenVisible = value\r\n        }\r\n\r\n    private var _isEpgScreenVisible by mutableStateOf(false)\r\n    var isEpgScreenVisible\r\n        get() = _isEpgScreenVisible\r\n        set(value) {\r\n            _isEpgScreenVisible = value\r\n        }\r\n\r\n    private var _isChannelLineScreenVisible by mutableStateOf(false)\r\n    var isChannelLineScreenVisible\r\n        get() = _isChannelLineScreenVisible\r\n        set(value) {\r\n            _isChannelLineScreenVisible = value\r\n        }\r\n\r\n    private var _isVideoPlayerDisplayModeScreenVisible by mutableStateOf(false)\r\n    var isVideoPlayerDisplayModeScreenVisible\r\n        get() = _isVideoPlayerDisplayModeScreenVisible\r\n        set(value) {\r\n            _isVideoPlayerDisplayModeScreenVisible = value\r\n        }\r\n\r\n    private var _isVideoTracksScreenVisible by mutableStateOf(false)\r\n    var isVideoTracksScreenVisible\r\n        get() = _isVideoTracksScreenVisible\r\n        set(value) {\r\n            _isVideoTracksScreenVisible = value\r\n        }\r\n\r\n    private var _isAudioTracksScreenVisible by mutableStateOf(false)\r\n    var isAudioTracksScreenVisible\r\n        get() = _isAudioTracksScreenVisible\r\n        set(value) {\r\n            _isAudioTracksScreenVisible = value\r\n        }\r\n\r\n    private var _isSubtitleTracksScreenVisible by mutableStateOf(false)\r\n    var isSubtitleTracksScreenVisible\r\n        get() = _isSubtitleTracksScreenVisible\r\n        set(value) {\r\n            _isSubtitleTracksScreenVisible = value\r\n        }\r\n\r\n    init {\r\n        val channelGroupList = channelGroupListProvider()\r\n\r\n        changeCurrentChannel(settingsViewModel.iptvChannelLastPlay.isEmptyOrElse {\r\n            channelGroupList.channelFirstOrNull() ?: Channel.EMPTY\r\n        })\r\n\r\n        videoPlayerState.onReady {\r\n            settingsViewModel.iptvChannelLinePlayableUrlList += currentChannelLine.url\r\n            settingsViewModel.iptvChannelLinePlayableHostList += currentChannelLine.url.urlHost()\r\n        }\r\n\r\n        videoPlayerState.onError {\r\n            if (_currentPlaybackEpgProgramme != null) return@onError\r\n\r\n            settingsViewModel.iptvChannelLinePlayableUrlList -= currentChannelLine.url\r\n            settingsViewModel.iptvChannelLinePlayableHostList -= currentChannelLine.url.urlHost()\r\n\r\n            if (_currentChannelLineIdx < _currentChannel.lineList.size - 1) {\r\n                changeCurrentChannel(_currentChannel, _currentChannelLineIdx + 1)\r\n            }\r\n        }\r\n\r\n        videoPlayerState.onInterrupt {\r\n            changeCurrentChannel(\r\n                _currentChannel,\r\n                _currentChannelLineIdx,\r\n                _currentPlaybackEpgProgramme\r\n            )\r\n        }\r\n\r\n        videoPlayerState.onIsBuffering { isBuffering ->\r\n            if (isBuffering) {\r\n                _isTempChannelScreenVisible = true\r\n            } else {\r\n                _tempChannelScreenHideJob?.cancel()\r\n                _tempChannelScreenHideJob = coroutineScope.launch {\r\n                    val name = _currentChannel.name\r\n                    val lineIdx = _currentChannelLineIdx\r\n                    delay(Constants.UI_TEMP_CHANNEL_SCREEN_SHOW_DURATION)\r\n                    if (name == _currentChannel.name && lineIdx == _currentChannelLineIdx) {\r\n                        _isTempChannelScreenVisible = false\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    private fun getPrevFavoriteChannel(): Channel? {\r\n        if (!settingsViewModel.iptvChannelFavoriteListVisible) return null\r\n\r\n        val channelGroupList = channelGroupListProvider()\r\n        val favoriteChannelList = favoriteChannelListProvider()\r\n\r\n        if (_currentChannel !in favoriteChannelList) return null\r\n\r\n        val currentIdx = favoriteChannelList.indexOf(_currentChannel)\r\n\r\n        return favoriteChannelList.getOrElse(currentIdx - 1) {\r\n            if (settingsViewModel.iptvChannelChangeListLoop) favoriteChannelList.lastOrNull()\r\n            else channelGroupList.channelLastOrNull()\r\n        }\r\n    }\r\n\r\n    private fun getNextFavoriteChannel(): Channel? {\r\n        if (!settingsViewModel.iptvChannelFavoriteListVisible) return null\r\n\r\n        val channelGroupList = channelGroupListProvider()\r\n        val favoriteChannelList = favoriteChannelListProvider()\r\n\r\n        if (_currentChannel !in favoriteChannelList) return null\r\n\r\n        val currentIdx = favoriteChannelList.indexOf(_currentChannel)\r\n\r\n        return favoriteChannelList.getOrElse(currentIdx + 1) {\r\n            if (settingsViewModel.iptvChannelChangeListLoop) favoriteChannelList.firstOrNull()\r\n            else channelGroupList.channelFirstOrNull()\r\n        }\r\n    }\r\n\r\n    private fun getPrevChannel(): Channel {\r\n        return getPrevFavoriteChannel() ?: run {\r\n            val channelGroupList = channelGroupListProvider()\r\n            return if (settingsViewModel.iptvChannelChangeListLoop) {\r\n                val group =\r\n                    channelGroupList.getOrElse(channelGroupList.channelGroupIdx(_currentChannel)) { channelGroupList.first() }\r\n                val currentIdx = group.channelList.indexOf(_currentChannel)\r\n                group.channelList.getOrElse(currentIdx - 1) { group.channelList.last() }\r\n            } else {\r\n                val currentIdx = channelGroupList.channelIdx(_currentChannel)\r\n                channelGroupList.channelList.getOrElse(currentIdx - 1) {\r\n                    channelGroupList.channelLastOrNull() ?: Channel()\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    private fun getNextChannel(): Channel {\r\n        return getNextFavoriteChannel() ?: run {\r\n            val channelGroupList = channelGroupListProvider()\r\n            return if (settingsViewModel.iptvChannelChangeListLoop) {\r\n                val group =\r\n                    channelGroupList.getOrElse(channelGroupList.channelGroupIdx(_currentChannel)) { channelGroupList.first() }\r\n                val currentIdx = group.channelList.indexOf(_currentChannel)\r\n                group.channelList.getOrElse(currentIdx + 1) { group.channelList.first() }\r\n            } else {\r\n                val currentIdx = channelGroupList.channelIdx(_currentChannel)\r\n                channelGroupList.channelList.getOrElse(currentIdx + 1) {\r\n                    channelGroupList.channelFirstOrNull() ?: Channel()\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    private fun getLineIdx(lineList: ChannelLineList, lineIdx: Int? = null): Int {\r\n        val idx = if (lineIdx == null) {\r\n            val idx = lineList.indexOfFirst { line ->\r\n                settingsViewModel.iptvChannelLinePlayableUrlList.contains(line.url)\r\n            }\r\n\r\n            if (idx < 0) {\r\n                lineList.indexOfFirst { line ->\r\n                    settingsViewModel.iptvChannelLinePlayableHostList.contains(line.url.urlHost())\r\n                }\r\n            } else idx\r\n        } else (lineIdx + lineList.size) % lineList.size\r\n\r\n        return max(0, min(idx, lineList.size - 1))\r\n    }\r\n\r\n    fun changeCurrentChannel(\r\n        channel: Channel,\r\n        lineIdx: Int? = null,\r\n        playbackEpgProgramme: EpgProgramme? = null,\r\n    ) {\r\n        settingsViewModel.iptvChannelLastPlay = channel\r\n\r\n        if (channel == _currentChannel && lineIdx == _currentChannelLineIdx && playbackEpgProgramme == _currentPlaybackEpgProgramme) return\r\n\r\n        if (channel == _currentChannel && lineIdx != _currentChannelLineIdx) {\r\n            settingsViewModel.iptvChannelLinePlayableUrlList -= currentChannelLine.url\r\n            settingsViewModel.iptvChannelLinePlayableHostList -= currentChannelLine.url.urlHost()\r\n        }\r\n\r\n        _isTempChannelScreenVisible = true\r\n\r\n        _currentChannel = channel\r\n        _currentChannelLineIdx = getLineIdx(_currentChannel.lineList, lineIdx)\r\n\r\n        _currentPlaybackEpgProgramme = playbackEpgProgramme\r\n\r\n        var url = currentChannelLine.url\r\n        if (_currentPlaybackEpgProgramme != null) {\r\n            val timeFormat = SimpleDateFormat(\"yyyyMMddHHmmss\", Locale.getDefault())\r\n            val query = listOf(\r\n                \"playseek=\",\r\n                timeFormat.format(_currentPlaybackEpgProgramme!!.startAt),\r\n                \"-\",\r\n                timeFormat.format(_currentPlaybackEpgProgramme!!.endAt),\r\n            ).joinToString(\"\")\r\n            url = if (URI(url).query.isNullOrBlank()) \"$url?$query\" else \"$url&$query\"\r\n            url = ChannelUtil.urlToCanPlayback(url)\r\n        }\r\n        val line = currentChannelLine.copy(url = url)\r\n\r\n        log.d(\"播放${_currentChannel.name}（${_currentChannelLineIdx + 1}/${_currentChannel.lineList.size}）: $line\")\r\n\r\n        if (line.url.startsWith(\"webview://\")) {\r\n            videoPlayerState.metadata = VideoPlayer.Metadata()\r\n            videoPlayerState.stop()\r\n        } else {\r\n            videoPlayerState.prepare(line)\r\n        }\r\n    }\r\n\r\n    fun changeCurrentChannelToPrev() {\r\n        changeCurrentChannel(getPrevChannel())\r\n    }\r\n\r\n    fun changeCurrentChannelToNext() {\r\n        changeCurrentChannel(getNextChannel())\r\n    }\r\n\r\n    fun reverseEpgProgrammeOrNot(channel: Channel, programme: EpgProgramme) {\r\n        val reverse = settingsViewModel.epgChannelReserveList.firstOrNull {\r\n            it.test(channel, programme)\r\n        }\r\n\r\n        if (reverse != null) {\r\n            settingsViewModel.epgChannelReserveList =\r\n                EpgProgrammeReserveList(settingsViewModel.epgChannelReserveList - reverse)\r\n            Snackbar.show(\"取消预约：${reverse.channel} - ${reverse.programme}\")\r\n        } else {\r\n            val newReserve = EpgProgrammeReserve(\r\n                channel = channel.name,\r\n                programme = programme.title,\r\n                startAt = programme.startAt,\r\n                endAt = programme.endAt,\r\n            )\r\n\r\n            settingsViewModel.epgChannelReserveList =\r\n                EpgProgrammeReserveList(settingsViewModel.epgChannelReserveList + newReserve)\r\n            Snackbar.show(\"已预约：${channel.name} - ${programme.title}\")\r\n        }\r\n    }\r\n\r\n    fun supportPlayback(\r\n        channel: Channel = _currentChannel,\r\n        lineIdx: Int? = _currentChannelLineIdx,\r\n    ): Boolean {\r\n        val currentLineIdx = getLineIdx(channel.lineList, lineIdx)\r\n        return ChannelUtil.urlSupportPlayback(channel.lineList[currentLineIdx].url)\r\n    }\r\n}\r\n\r\n@Composable\r\nfun rememberMainContentState(\r\n    coroutineScope: CoroutineScope = rememberCoroutineScope(),\r\n    videoPlayerState: VideoPlayerState = rememberVideoPlayerState(),\r\n    channelGroupListProvider: () -> ChannelGroupList = { ChannelGroupList() },\r\n    favoriteChannelListProvider: () -> ChannelList = { ChannelList() },\r\n    settingsViewModel: SettingsViewModel = settingsVM,\r\n): MainContentState {\r\n    val favoriteChannelListProviderUpdated by rememberUpdatedState(favoriteChannelListProvider)\r\n\r\n    return remember(settingsVM.videoPlayerCore) {\r\n        MainContentState(\r\n            coroutineScope = coroutineScope,\r\n            videoPlayerState = videoPlayerState,\r\n            channelGroupListProvider = channelGroupListProvider,\r\n            favoriteChannelListProvider = favoriteChannelListProviderUpdated,\r\n            settingsViewModel = settingsViewModel,\r\n        )\r\n    }\r\n}
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/main/components/MainContentState.kt b/tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/main/components/MainContentState.kt
--- a/tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/main/components/MainContentState.kt	(revision ****************************************)
+++ b/tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/main/components/MainContentState.kt	(date 1755504687848)
@@ -286,15 +286,24 @@
 
         var url = currentChannelLine.url
         if (_currentPlaybackEpgProgramme != null) {
-            val timeFormat = SimpleDateFormat("yyyyMMddHHmmss", Locale.getDefault())
-            val query = listOf(
-                "playseek=",
-                timeFormat.format(_currentPlaybackEpgProgramme!!.startAt),
-                "-",
-                timeFormat.format(_currentPlaybackEpgProgramme!!.endAt),
-            ).joinToString("")
-            url = if (URI(url).query.isNullOrBlank()) "$url?$query" else "$url&$query"
-            url = ChannelUtil.urlToCanPlayback(url)
+            // 优先使用catchup-source，如果没有则使用传统的playseek方式
+            if (!currentChannelLine.catchupSource.isNullOrBlank()) {
+                url = ChannelUtil.buildCatchupUrl(
+                    currentChannelLine.catchupSource!!,
+                    _currentPlaybackEpgProgramme!!.startAt,
+                    _currentPlaybackEpgProgramme!!.endAt
+                )
+            } else {
+                val timeFormat = SimpleDateFormat("yyyyMMddHHmmss", Locale.getDefault())
+                val query = listOf(
+                    "playseek=",
+                    timeFormat.format(_currentPlaybackEpgProgramme!!.startAt),
+                    "-",
+                    timeFormat.format(_currentPlaybackEpgProgramme!!.endAt),
+                ).joinToString("")
+                url = if (URI(url).query.isNullOrBlank()) "$url?$query" else "$url&$query"
+                url = ChannelUtil.urlToCanPlayback(url)
+            }
         }
         val line = currentChannelLine.copy(url = url)
 
@@ -344,7 +353,7 @@
         lineIdx: Int? = _currentChannelLineIdx,
     ): Boolean {
         val currentLineIdx = getLineIdx(channel.lineList, lineIdx)
-        return ChannelUtil.urlSupportPlayback(channel.lineList[currentLineIdx].url)
+        return ChannelUtil.channelLineSupportPlayback(channel.lineList[currentLineIdx])
     }
 }
 
Index: tv/src/main/java/top/yogiczy/mytv/tv/ui/utils/Configs.kt
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package top.yogiczy.mytv.tv.ui.utils\r\n\r\nimport kotlinx.serialization.Serializable\r\nimport kotlinx.serialization.encodeToString\r\nimport top.yogiczy.mytv.core.data.entities.channel.Channel\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelFavoriteList\r\nimport top.yogiczy.mytv.core.data.entities.epg.EpgProgrammeReserveList\r\nimport top.yogiczy.mytv.core.data.entities.epgsource.EpgSource\r\nimport top.yogiczy.mytv.core.data.entities.epgsource.EpgSourceList\r\nimport top.yogiczy.mytv.core.data.entities.iptvsource.IptvSource\r\nimport top.yogiczy.mytv.core.data.entities.iptvsource.IptvSourceList\r\nimport top.yogiczy.mytv.core.data.utils.Constants\r\nimport top.yogiczy.mytv.core.data.utils.Globals\r\nimport top.yogiczy.mytv.core.data.utils.SP\r\nimport top.yogiczy.mytv.tv.sync.CloudSyncProvider\r\nimport top.yogiczy.mytv.tv.ui.screen.Screens\r\nimport top.yogiczy.mytv.tv.ui.screen.components.AppThemeDef\r\nimport top.yogiczy.mytv.tv.ui.screensold.videoplayer.VideoPlayerDisplayMode\r\n\r\n/**\r\n * 应用配置\r\n */\r\nobject Configs {\r\n    enum class KEY {\r\n        /** ==================== 应用 ==================== */\r\n        /** 开机自启 */\r\n        APP_BOOT_LAUNCH,\r\n\r\n        /** 画中画启用 */\r\n        APP_PIP_ENABLE,\r\n\r\n        /** 上一次最新版本 */\r\n        APP_LAST_LATEST_VERSION,\r\n\r\n        /** 协议已同意 */\r\n        APP_AGREEMENT_AGREED,\r\n\r\n        /** 打开直接进入直播 */\r\n        APP_STARTUP_SCREEN,\r\n\r\n        /** ==================== 调式 ==================== */\r\n        /** 开发者模式 */\r\n        DEBUG_DEVELOPER_MODE,\r\n\r\n        /** 显示fps */\r\n        DEBUG_SHOW_FPS,\r\n\r\n        /** 播放器详细信息 */\r\n        DEBUG_SHOW_VIDEO_PLAYER_METADATA,\r\n\r\n        /** 显示布局网格 */\r\n        DEBUG_SHOW_LAYOUT_GRIDS,\r\n\r\n        /** ==================== 直播源 ==================== */\r\n        /** 当前直播源 */\r\n        IPTV_SOURCE_CURRENT,\r\n\r\n        /** 直播源列表 */\r\n        IPTV_SOURCE_LIST,\r\n\r\n        /** 直播源缓存时间（毫秒） */\r\n        IPTV_SOURCE_CACHE_TIME,\r\n\r\n        /** 直播源分组隐藏列表 */\r\n        IPTV_CHANNEL_GROUP_HIDDEN_LIST,\r\n\r\n        /** 混合模式 */\r\n        IPTV_HYBRID_MODE,\r\n\r\n        /** 相似频道合并 */\r\n        IPTV_SIMILAR_CHANNEL_MERGE,\r\n\r\n        /** 频道图标提供 */\r\n        IPTV_CHANNEL_LOGO_PROVIDER,\r\n\r\n        /** 频道图标覆盖 */\r\n        IPTV_CHANNEL_LOGO_OVERRIDE,\r\n\r\n        /** 是否启用直播源频道收藏 */\r\n        IPTV_CHANNEL_FAVORITE_ENABLE,\r\n\r\n        /** 显示直播源频道收藏列表 */\r\n        IPTV_CHANNEL_FAVORITE_LIST_VISIBLE,\r\n\r\n        /** 直播源频道收藏列表 */\r\n        IPTV_CHANNEL_FAVORITE_LIST,\r\n\r\n        /** 上一次播放频道 */\r\n        IPTV_CHANNEL_LAST_PLAY,\r\n\r\n        /** 直播源线路可播放host列表 */\r\n        IPTV_CHANNEL_LINE_PLAYABLE_HOST_LIST,\r\n\r\n        /** 直播源线路可播放地址列表 */\r\n        IPTV_CHANNEL_LINE_PLAYABLE_URL_LIST,\r\n\r\n        /** 换台反转 */\r\n        IPTV_CHANNEL_CHANGE_FLIP,\r\n\r\n        /** 是否启用数字选台 */\r\n        IPTV_CHANNEL_NO_SELECT_ENABLE,\r\n\r\n        /** 换台列表首尾循环 **/\r\n        IPTV_CHANNEL_CHANGE_LIST_LOOP,\r\n\r\n        /** ==================== 节目单 ==================== */\r\n        /** 启用节目单 */\r\n        EPG_ENABLE,\r\n\r\n        /** 当前节目单来源 */\r\n        EPG_SOURCE_CURRENT,\r\n\r\n        /** 节目单来源列表 */\r\n        EPG_SOURCE_LIST,\r\n\r\n        /** 节目单刷新时间阈值（小时） */\r\n        EPG_REFRESH_TIME_THRESHOLD,\r\n\r\n        /** 节目单跟随直播源 */\r\n        EPG_SOURCE_FOLLOW_IPTV,\r\n\r\n        /** 节目预约列表 */\r\n        EPG_CHANNEL_RESERVE_LIST,\r\n\r\n        /** ==================== 界面 ==================== */\r\n        /** 显示节目进度 */\r\n        UI_SHOW_EPG_PROGRAMME_PROGRESS,\r\n\r\n        /** 显示常驻节目进度 */\r\n        UI_SHOW_EPG_PROGRAMME_PERMANENT_PROGRESS,\r\n\r\n        /** 显示台标 */\r\n        UI_SHOW_CHANNEL_LOGO,\r\n\r\n        /** 显示频道预览 */\r\n        UI_SHOW_CHANNEL_PREVIEW,\r\n\r\n        /** 使用经典选台界面 */\r\n        UI_USE_CLASSIC_PANEL_SCREEN,\r\n\r\n        /** 界面密度缩放比例 */\r\n        UI_DENSITY_SCALE_RATIO,\r\n\r\n        /** 界面字体缩放比例 */\r\n        UI_FONT_SCALE_RATIO,\r\n\r\n        /** 时间显示模式 */\r\n        UI_TIME_SHOW_MODE,\r\n\r\n        /** 焦点优化 */\r\n        UI_FOCUS_OPTIMIZE,\r\n\r\n        /** 自动关闭界面延时 */\r\n        UI_SCREEN_AUTO_CLOSE_DELAY,\r\n\r\n        /** ==================== 更新 ==================== */\r\n        /** 更新强提醒 */\r\n        UPDATE_FORCE_REMIND,\r\n\r\n        /** 更新通道 */\r\n        UPDATE_CHANNEL,\r\n\r\n        /** ==================== 播放器 ==================== */\r\n        /** 播放器 内核 */\r\n        VIDEO_PLAYER_CORE,\r\n\r\n        /** 播放器 渲染方式 */\r\n        VIDEO_PLAYER_RENDER_MODE,\r\n\r\n        /** 播放器 自定义ua */\r\n        VIDEO_PLAYER_USER_AGENT,\r\n\r\n        /** 播放器 自定义headers */\r\n        VIDEO_PLAYER_HEADERS,\r\n\r\n        /** 播放器 加载超时 */\r\n        VIDEO_PLAYER_LOAD_TIMEOUT,\r\n\r\n        /** 播放器 显示模式 */\r\n        VIDEO_PLAYER_DISPLAY_MODE,\r\n\r\n        /** 播放器 强制音频软解 */\r\n        VIDEO_PLAYER_FORCE_AUDIO_SOFT_DECODE,\r\n\r\n        /** 播放器 停止上一媒体项 */\r\n        VIDEO_PLAYER_STOP_PREVIOUS_MEDIA_ITEM,\r\n\r\n        /** 播放器 跳过同一VSync渲染多帧 */\r\n        VIDEO_PLAYER_SKIP_MULTIPLE_FRAMES_ON_SAME_VSYNC,\r\n\r\n        /** ==================== 主题 ==================== */\r\n        /** 当前应用主题 */\r\n        THEME_APP_CURRENT,\r\n\r\n        /** ==================== 云同步 ==================== */\r\n        /** 云同步 自动拉取 */\r\n        CLOUD_SYNC_AUTO_PULL,\r\n\r\n        /** 云同步 提供商 */\r\n        CLOUD_SYNC_PROVIDER,\r\n\r\n        /** 云同步 github gist id */\r\n        CLOUD_SYNC_GITHUB_GIST_ID,\r\n\r\n        /** 云同步 github gist token */\r\n        CLOUD_SYNC_GITHUB_GIST_TOKEN,\r\n\r\n        /** 云同步 gitee gist id */\r\n        CLOUD_SYNC_GITEE_GIST_ID,\r\n\r\n        /** 云同步 gitee gist token */\r\n        CLOUD_SYNC_GITEE_GIST_TOKEN,\r\n\r\n        /** 云同步 网络链接 */\r\n        CLOUD_SYNC_NETWORK_URL,\r\n\r\n        /** 云同步 本地文件 */\r\n        CLOUD_SYNC_LOCAL_FILE,\r\n\r\n        /** 云同步 webdav url */\r\n        CLOUD_SYNC_WEBDAV_URL,\r\n\r\n        /** 云同步 webdav 用户名 */\r\n        CLOUD_SYNC_WEBDAV_USERNAME,\r\n\r\n        /** 云同步 webdav 密码 */\r\n        CLOUD_SYNC_WEBDAV_PASSWORD,\r\n\r\n        /** 肥羊 AllInOne 文件路径 */\r\n        FEIYANG_ALLINONE_FILE_PATH,\r\n    }\r\n\r\n    /** ==================== 应用 ==================== */\r\n    /** 开机自启 */\r\n    var appBootLaunch: Boolean\r\n        get() = SP.getBoolean(KEY.APP_BOOT_LAUNCH.name, false)\r\n        set(value) = SP.putBoolean(KEY.APP_BOOT_LAUNCH.name, value)\r\n\r\n    /** 画中画启用 */\r\n    var appPipEnable: Boolean\r\n        get() = SP.getBoolean(KEY.APP_PIP_ENABLE.name, false)\r\n        set(value) = SP.putBoolean(KEY.APP_PIP_ENABLE.name, value)\r\n\r\n    /** 上一次最新版本 */\r\n    var appLastLatestVersion: String\r\n        get() = SP.getString(KEY.APP_LAST_LATEST_VERSION.name, \"\")\r\n        set(value) = SP.putString(KEY.APP_LAST_LATEST_VERSION.name, value)\r\n\r\n    /** 协议已同意 */\r\n    var appAgreementAgreed: Boolean\r\n        get() = SP.getBoolean(KEY.APP_AGREEMENT_AGREED.name, false)\r\n        set(value) = SP.putBoolean(KEY.APP_AGREEMENT_AGREED.name, value)\r\n\r\n    /** 起始界面 */\r\n    var appStartupScreen: String\r\n        get() = SP.getString(KEY.APP_STARTUP_SCREEN.name, Screens.Dashboard.name)\r\n        set(value) = SP.putString(KEY.APP_STARTUP_SCREEN.name, value)\r\n\r\n    /** ==================== 调式 ==================== */\r\n    /** 开发者模式 */\r\n    var debugDeveloperMode: Boolean\r\n        get() = SP.getBoolean(KEY.DEBUG_DEVELOPER_MODE.name, false)\r\n        set(value) = SP.putBoolean(KEY.DEBUG_DEVELOPER_MODE.name, value)\r\n\r\n    /** 显示fps */\r\n    var debugShowFps: Boolean\r\n        get() = SP.getBoolean(KEY.DEBUG_SHOW_FPS.name, false)\r\n        set(value) = SP.putBoolean(KEY.DEBUG_SHOW_FPS.name, value)\r\n\r\n    /** 播放器详细信息 */\r\n    var debugShowVideoPlayerMetadata: Boolean\r\n        get() = SP.getBoolean(KEY.DEBUG_SHOW_VIDEO_PLAYER_METADATA.name, false)\r\n        set(value) = SP.putBoolean(KEY.DEBUG_SHOW_VIDEO_PLAYER_METADATA.name, value)\r\n\r\n    /** 显示布局网格 */\r\n    var debugShowLayoutGrids: Boolean\r\n        get() = SP.getBoolean(KEY.DEBUG_SHOW_LAYOUT_GRIDS.name, false)\r\n        set(value) = SP.putBoolean(KEY.DEBUG_SHOW_LAYOUT_GRIDS.name, value)\r\n\r\n    /** ==================== 直播源 ==================== */\r\n    /** 当前直播源 */\r\n    var iptvSourceCurrent: IptvSource\r\n        get() = Globals.json.decodeFromString(SP.getString(KEY.IPTV_SOURCE_CURRENT.name, \"\")\r\n            .ifBlank { Globals.json.encodeToString(Constants.IPTV_SOURCE_LIST.first()) })\r\n        set(value) = SP.putString(KEY.IPTV_SOURCE_CURRENT.name, Globals.json.encodeToString(value))\r\n\r\n    /** 直播源列表 */\r\n    var iptvSourceList: IptvSourceList\r\n        get() = Globals.json.decodeFromString(\r\n            SP.getString(KEY.IPTV_SOURCE_LIST.name, Globals.json.encodeToString(IptvSourceList()))\r\n        )\r\n        set(value) = SP.putString(KEY.IPTV_SOURCE_LIST.name, Globals.json.encodeToString(value))\r\n\r\n    /** 直播源缓存时间（毫秒） */\r\n    var iptvSourceCacheTime: Long\r\n        get() = SP.getLong(KEY.IPTV_SOURCE_CACHE_TIME.name, Constants.IPTV_SOURCE_CACHE_TIME)\r\n        set(value) = SP.putLong(KEY.IPTV_SOURCE_CACHE_TIME.name, value)\r\n\r\n    /** 直播源分组隐藏列表 */\r\n    var iptvChannelGroupHiddenList: Set<String>\r\n        get() = SP.getStringSet(KEY.IPTV_CHANNEL_GROUP_HIDDEN_LIST.name, emptySet())\r\n        set(value) = SP.putStringSet(KEY.IPTV_CHANNEL_GROUP_HIDDEN_LIST.name, value)\r\n\r\n    /** 混合模式 */\r\n    var iptvHybridMode: IptvHybridMode\r\n        get() = IptvHybridMode.fromValue(\r\n            SP.getInt(KEY.IPTV_HYBRID_MODE.name, IptvHybridMode.DISABLE.value)\r\n        )\r\n        set(value) = SP.putInt(KEY.IPTV_HYBRID_MODE.name, value.value)\r\n\r\n    /** 相似频道合并 */\r\n    var iptvSimilarChannelMerge: Boolean\r\n        get() = SP.getBoolean(KEY.IPTV_SIMILAR_CHANNEL_MERGE.name, false)\r\n        set(value) = SP.putBoolean(KEY.IPTV_SIMILAR_CHANNEL_MERGE.name, value)\r\n\r\n    /** 频道图标提供 */\r\n    var iptvChannelLogoProvider: String\r\n        get() = SP.getString(KEY.IPTV_CHANNEL_LOGO_PROVIDER.name, Constants.CHANNEL_LOGO_PROVIDER)\r\n        set(value) = SP.putString(KEY.IPTV_CHANNEL_LOGO_PROVIDER.name, value)\r\n\r\n    /** 频道图标覆盖 */\r\n    var iptvChannelLogoOverride: Boolean\r\n        get() = SP.getBoolean(KEY.IPTV_CHANNEL_LOGO_OVERRIDE.name, false)\r\n        set(value) = SP.putBoolean(KEY.IPTV_CHANNEL_LOGO_OVERRIDE.name, value)\r\n\r\n    /** 是否启用直播源频道收藏 */\r\n    var iptvChannelFavoriteEnable: Boolean\r\n        get() = SP.getBoolean(KEY.IPTV_CHANNEL_FAVORITE_ENABLE.name, true)\r\n        set(value) = SP.putBoolean(KEY.IPTV_CHANNEL_FAVORITE_ENABLE.name, value)\r\n\r\n    /** 显示直播源频道收藏列表 */\r\n    var iptvChannelFavoriteListVisible: Boolean\r\n        get() = SP.getBoolean(KEY.IPTV_CHANNEL_FAVORITE_LIST_VISIBLE.name, false)\r\n        set(value) = SP.putBoolean(KEY.IPTV_CHANNEL_FAVORITE_LIST_VISIBLE.name, value)\r\n\r\n    /** 直播源频道收藏列表 */\r\n    var iptvChannelFavoriteList: ChannelFavoriteList\r\n        get() = Globals.json.decodeFromString(\r\n            SP.getString(\r\n                KEY.IPTV_CHANNEL_FAVORITE_LIST.name,\r\n                Globals.json.encodeToString(ChannelFavoriteList())\r\n            )\r\n        )\r\n        set(value) = SP.putString(\r\n            KEY.IPTV_CHANNEL_FAVORITE_LIST.name,\r\n            Globals.json.encodeToString(value)\r\n        )\r\n\r\n    /** 上一次播放频道 */\r\n    var iptvChannelLastPlay: Channel\r\n        get() = Globals.json.decodeFromString(\r\n            SP.getString(\r\n                KEY.IPTV_CHANNEL_LAST_PLAY.name,\r\n                Globals.json.encodeToString(Channel.EMPTY)\r\n            )\r\n        )\r\n        set(value) = SP.putString(\r\n            KEY.IPTV_CHANNEL_LAST_PLAY.name,\r\n            Globals.json.encodeToString(value)\r\n        )\r\n\r\n    /** 直播源线路可播放host列表 */\r\n    var iptvChannelLinePlayableHostList: Set<String>\r\n        get() = SP.getStringSet(KEY.IPTV_CHANNEL_LINE_PLAYABLE_HOST_LIST.name, emptySet())\r\n        set(value) = SP.putStringSet(KEY.IPTV_CHANNEL_LINE_PLAYABLE_HOST_LIST.name, value)\r\n\r\n    /** 直播源线路可播放地址列表 */\r\n    // IPTV_CHANNEL_LINE_PLAYABLE_URL_LIST,\r\n    var iptvChannelLinePlayableUrlList: Set<String>\r\n        get() = SP.getStringSet(KEY.IPTV_CHANNEL_LINE_PLAYABLE_URL_LIST.name, emptySet())\r\n        set(value) = SP.putStringSet(KEY.IPTV_CHANNEL_LINE_PLAYABLE_URL_LIST.name, value)\r\n\r\n    /** 换台反转 */\r\n    var iptvChannelChangeFlip: Boolean\r\n        get() = SP.getBoolean(KEY.IPTV_CHANNEL_CHANGE_FLIP.name, false)\r\n        set(value) = SP.putBoolean(KEY.IPTV_CHANNEL_CHANGE_FLIP.name, value)\r\n\r\n    /** 是否启用数字选台 */\r\n    var iptvChannelNoSelectEnable: Boolean\r\n        get() = SP.getBoolean(KEY.IPTV_CHANNEL_NO_SELECT_ENABLE.name, true)\r\n        set(value) = SP.putBoolean(KEY.IPTV_CHANNEL_NO_SELECT_ENABLE.name, value)\r\n\r\n    /** 换台列表首尾循环 **/\r\n    var iptvChannelChangeListLoop: Boolean\r\n        get() = SP.getBoolean(KEY.IPTV_CHANNEL_CHANGE_LIST_LOOP.name, false)\r\n        set(value) = SP.putBoolean(KEY.IPTV_CHANNEL_CHANGE_LIST_LOOP.name, value)\r\n\r\n    /** ==================== 节目单 ==================== */\r\n    /** 启用节目单 */\r\n    var epgEnable: Boolean\r\n        get() = SP.getBoolean(KEY.EPG_ENABLE.name, true)\r\n        set(value) = SP.putBoolean(KEY.EPG_ENABLE.name, value)\r\n\r\n    /** 当前节目单来源 */\r\n    var epgSourceCurrent: EpgSource\r\n        get() = Globals.json.decodeFromString(SP.getString(KEY.EPG_SOURCE_CURRENT.name, \"\")\r\n            .ifBlank { Globals.json.encodeToString(Constants.EPG_SOURCE_LIST.first()) })\r\n        set(value) = SP.putString(KEY.EPG_SOURCE_CURRENT.name, Globals.json.encodeToString(value))\r\n\r\n    /** 节目单来源列表 */\r\n    var epgSourceList: EpgSourceList\r\n        get() = Globals.json.decodeFromString(\r\n            SP.getString(KEY.EPG_SOURCE_LIST.name, Globals.json.encodeToString(EpgSourceList()))\r\n        )\r\n        set(value) = SP.putString(KEY.EPG_SOURCE_LIST.name, Globals.json.encodeToString(value))\r\n\r\n    /** 节目单刷新时间阈值（小时） */\r\n    var epgRefreshTimeThreshold: Int\r\n        get() = SP.getInt(KEY.EPG_REFRESH_TIME_THRESHOLD.name, Constants.EPG_REFRESH_TIME_THRESHOLD)\r\n        set(value) = SP.putInt(KEY.EPG_REFRESH_TIME_THRESHOLD.name, value)\r\n\r\n    /** 节目单跟随直播源 */\r\n    var epgSourceFollowIptv: Boolean\r\n        get() = SP.getBoolean(KEY.EPG_SOURCE_FOLLOW_IPTV.name, false)\r\n        set(value) = SP.putBoolean(KEY.EPG_SOURCE_FOLLOW_IPTV.name, value)\r\n\r\n    /** 节目预约列表 */\r\n    var epgChannelReserveList: EpgProgrammeReserveList\r\n        get() = Globals.json.decodeFromString(\r\n            SP.getString(\r\n                KEY.EPG_CHANNEL_RESERVE_LIST.name,\r\n                Globals.json.encodeToString(EpgProgrammeReserveList())\r\n            )\r\n        )\r\n        set(value) = SP.putString(\r\n            KEY.EPG_CHANNEL_RESERVE_LIST.name,\r\n            Globals.json.encodeToString(value)\r\n        )\r\n\r\n    /** ==================== 界面 ==================== */\r\n    /** 显示节目进度 */\r\n    var uiShowEpgProgrammeProgress: Boolean\r\n        get() = SP.getBoolean(KEY.UI_SHOW_EPG_PROGRAMME_PROGRESS.name, true)\r\n        set(value) = SP.putBoolean(KEY.UI_SHOW_EPG_PROGRAMME_PROGRESS.name, value)\r\n\r\n    /** 显示常驻节目进度 */\r\n    var uiShowEpgProgrammePermanentProgress: Boolean\r\n        get() = SP.getBoolean(KEY.UI_SHOW_EPG_PROGRAMME_PERMANENT_PROGRESS.name, false)\r\n        set(value) = SP.putBoolean(KEY.UI_SHOW_EPG_PROGRAMME_PERMANENT_PROGRESS.name, value)\r\n\r\n    /** 显示台标 */\r\n    var uiShowChannelLogo: Boolean\r\n        get() = SP.getBoolean(KEY.UI_SHOW_CHANNEL_LOGO.name, true)\r\n        set(value) = SP.putBoolean(KEY.UI_SHOW_CHANNEL_LOGO.name, value)\r\n\r\n    /** 显示频道预览 */\r\n    var uiShowChannelPreview: Boolean\r\n        get() = SP.getBoolean(KEY.UI_SHOW_CHANNEL_PREVIEW.name, false)\r\n        set(value) = SP.putBoolean(KEY.UI_SHOW_CHANNEL_PREVIEW.name, value)\r\n\r\n    /** 使用经典选台界面 */\r\n    var uiUseClassicPanelScreen: Boolean\r\n        get() = SP.getBoolean(KEY.UI_USE_CLASSIC_PANEL_SCREEN.name, false)\r\n        set(value) = SP.putBoolean(KEY.UI_USE_CLASSIC_PANEL_SCREEN.name, value)\r\n\r\n    /** 界面密度缩放比例 */\r\n    var uiDensityScaleRatio: Float\r\n        get() = SP.getFloat(KEY.UI_DENSITY_SCALE_RATIO.name, 0f)\r\n        set(value) = SP.putFloat(KEY.UI_DENSITY_SCALE_RATIO.name, value)\r\n\r\n    /** 界面字体缩放比例 */\r\n    var uiFontScaleRatio: Float\r\n        get() = SP.getFloat(KEY.UI_FONT_SCALE_RATIO.name, 1f)\r\n        set(value) = SP.putFloat(KEY.UI_FONT_SCALE_RATIO.name, value)\r\n\r\n    /** 时间显示模式 */\r\n    var uiTimeShowMode: UiTimeShowMode\r\n        get() = UiTimeShowMode.fromValue(\r\n            SP.getInt(KEY.UI_TIME_SHOW_MODE.name, UiTimeShowMode.HIDDEN.value)\r\n        )\r\n        set(value) = SP.putInt(KEY.UI_TIME_SHOW_MODE.name, value.value)\r\n\r\n    /** 焦点优化 */\r\n    var uiFocusOptimize: Boolean\r\n        get() = SP.getBoolean(KEY.UI_FOCUS_OPTIMIZE.name, true)\r\n        set(value) = SP.putBoolean(KEY.UI_FOCUS_OPTIMIZE.name, value)\r\n\r\n    /** 自动关闭界面延时 */\r\n    var uiScreenAutoCloseDelay: Long\r\n        get() =\r\n            SP.getLong(KEY.UI_SCREEN_AUTO_CLOSE_DELAY.name, Constants.UI_SCREEN_AUTO_CLOSE_DELAY)\r\n        set(value) = SP.putLong(KEY.UI_SCREEN_AUTO_CLOSE_DELAY.name, value)\r\n\r\n    /** ==================== 更新 ==================== */\r\n    /** 更新强提醒 */\r\n    var updateForceRemind: Boolean\r\n        get() = SP.getBoolean(KEY.UPDATE_FORCE_REMIND.name, false)\r\n        set(value) = SP.putBoolean(KEY.UPDATE_FORCE_REMIND.name, value)\r\n\r\n    /** 更新通道 */\r\n    var updateChannel: String\r\n        get() = SP.getString(KEY.UPDATE_CHANNEL.name, \"stable\")\r\n        set(value) = SP.putString(KEY.UPDATE_CHANNEL.name, value)\r\n\r\n    /** ==================== 播放器 ==================== */\r\n    /** 播放器 内核 */\r\n    var videoPlayerCore: VideoPlayerCore\r\n        get() = VideoPlayerCore.fromValue(\r\n            SP.getInt(KEY.VIDEO_PLAYER_CORE.name, VideoPlayerCore.MEDIA3.value)\r\n        )\r\n        set(value) = SP.putInt(KEY.VIDEO_PLAYER_CORE.name, value.value)\r\n\r\n    /** 播放器 渲染方式 */\r\n    var videoPlayerRenderMode: VideoPlayerRenderMode\r\n        get() = VideoPlayerRenderMode.fromValue(\r\n            SP.getInt(KEY.VIDEO_PLAYER_RENDER_MODE.name, VideoPlayerRenderMode.SURFACE_VIEW.value)\r\n        )\r\n        set(value) = SP.putInt(KEY.VIDEO_PLAYER_RENDER_MODE.name, value.value)\r\n\r\n    /** 播放器 自定义ua */\r\n    var videoPlayerUserAgent: String\r\n        get() = SP.getString(KEY.VIDEO_PLAYER_USER_AGENT.name, \"\").ifBlank {\r\n            Constants.VIDEO_PLAYER_USER_AGENT\r\n        }\r\n        set(value) = SP.putString(KEY.VIDEO_PLAYER_USER_AGENT.name, value)\r\n\r\n    /** 播放器 自定义headers */\r\n    var videoPlayerHeaders: String\r\n        get() = SP.getString(KEY.VIDEO_PLAYER_HEADERS.name, \"\")\r\n        set(value) = SP.putString(KEY.VIDEO_PLAYER_HEADERS.name, value)\r\n\r\n    /** 播放器 加载超时 */\r\n    var videoPlayerLoadTimeout: Long\r\n        get() = SP.getLong(KEY.VIDEO_PLAYER_LOAD_TIMEOUT.name, Constants.VIDEO_PLAYER_LOAD_TIMEOUT)\r\n        set(value) = SP.putLong(KEY.VIDEO_PLAYER_LOAD_TIMEOUT.name, value)\r\n\r\n    /** 播放器 显示模式 */\r\n    var videoPlayerDisplayMode: VideoPlayerDisplayMode\r\n        get() = VideoPlayerDisplayMode.fromValue(\r\n            SP.getInt(KEY.VIDEO_PLAYER_DISPLAY_MODE.name, VideoPlayerDisplayMode.ORIGINAL.value)\r\n        )\r\n        set(value) = SP.putInt(KEY.VIDEO_PLAYER_DISPLAY_MODE.name, value.value)\r\n\r\n    /** 播放器 强制音频软解 */\r\n    var videoPlayerForceAudioSoftDecode: Boolean\r\n        get() = SP.getBoolean(KEY.VIDEO_PLAYER_FORCE_AUDIO_SOFT_DECODE.name, false)\r\n        set(value) = SP.putBoolean(KEY.VIDEO_PLAYER_FORCE_AUDIO_SOFT_DECODE.name, value)\r\n\r\n    /** 播放器 停止上一媒体项 */\r\n    var videoPlayerStopPreviousMediaItem: Boolean\r\n        get() = SP.getBoolean(KEY.VIDEO_PLAYER_STOP_PREVIOUS_MEDIA_ITEM.name, true)\r\n        set(value) = SP.putBoolean(KEY.VIDEO_PLAYER_STOP_PREVIOUS_MEDIA_ITEM.name, value)\r\n\r\n    /** 播放器 跳过同一VSync渲染多帧 */\r\n    var videoPlayerSkipMultipleFramesOnSameVSync: Boolean\r\n        get() = SP.getBoolean(KEY.VIDEO_PLAYER_SKIP_MULTIPLE_FRAMES_ON_SAME_VSYNC.name, true)\r\n        set(value) = SP.putBoolean(KEY.VIDEO_PLAYER_SKIP_MULTIPLE_FRAMES_ON_SAME_VSYNC.name, value)\r\n\r\n    /** ==================== 主题 ==================== */\r\n    /** 当前应用主题 */\r\n    var themeAppCurrent: AppThemeDef?\r\n        get() = SP.getString(KEY.THEME_APP_CURRENT.name, \"\").let {\r\n            if (it.isBlank()) null else Globals.json.decodeFromString(it)\r\n        }\r\n        set(value) = SP.putString(\r\n            KEY.THEME_APP_CURRENT.name,\r\n            value?.let { Globals.json.encodeToString(value) } ?: \"\")\r\n\r\n    /** ==================== 云同步 ==================== */\r\n    /** 云同步 自动拉取 */\r\n    var cloudSyncAutoPull: Boolean\r\n        get() = SP.getBoolean(KEY.CLOUD_SYNC_AUTO_PULL.name, false)\r\n        set(value) = SP.putBoolean(KEY.CLOUD_SYNC_AUTO_PULL.name, value)\r\n\r\n    /** 云同步 提供商 */\r\n    var cloudSyncProvider: CloudSyncProvider\r\n        get() = CloudSyncProvider.fromValue(\r\n            SP.getInt(KEY.CLOUD_SYNC_PROVIDER.name, CloudSyncProvider.GITHUB_GIST.value)\r\n        )\r\n        set(value) = SP.putInt(KEY.CLOUD_SYNC_PROVIDER.name, value.value)\r\n\r\n    /** 云同步 github gist id */\r\n    var cloudSyncGithubGistId: String\r\n        get() = SP.getString(KEY.CLOUD_SYNC_GITHUB_GIST_ID.name, \"\")\r\n        set(value) = SP.putString(KEY.CLOUD_SYNC_GITHUB_GIST_ID.name, value)\r\n\r\n    /** 云同步 github gist token */\r\n    var cloudSyncGithubGistToken: String\r\n        get() = SP.getString(KEY.CLOUD_SYNC_GITHUB_GIST_TOKEN.name, \"\")\r\n        set(value) = SP.putString(KEY.CLOUD_SYNC_GITHUB_GIST_TOKEN.name, value)\r\n\r\n    /** 云同步 gitee gist id */\r\n    var cloudSyncGiteeGistId: String\r\n        get() = SP.getString(KEY.CLOUD_SYNC_GITEE_GIST_ID.name, \"\")\r\n        set(value) = SP.putString(KEY.CLOUD_SYNC_GITEE_GIST_ID.name, value)\r\n\r\n    /** 云同步 gitee gist token */\r\n    var cloudSyncGiteeGistToken: String\r\n        get() = SP.getString(KEY.CLOUD_SYNC_GITEE_GIST_TOKEN.name, \"\")\r\n        set(value) = SP.putString(KEY.CLOUD_SYNC_GITEE_GIST_TOKEN.name, value)\r\n\r\n    /** 云同步 网络链接 */\r\n    var cloudSyncNetworkUrl: String\r\n        get() = SP.getString(KEY.CLOUD_SYNC_NETWORK_URL.name, \"\")\r\n        set(value) = SP.putString(KEY.CLOUD_SYNC_NETWORK_URL.name, value)\r\n\r\n    /** 云同步 本地文件 */\r\n    var cloudSyncLocalFilePath: String\r\n        get() = SP.getString(KEY.CLOUD_SYNC_LOCAL_FILE.name, \"\")\r\n        set(value) = SP.putString(KEY.CLOUD_SYNC_LOCAL_FILE.name, value)\r\n\r\n    /** 云同步 webdav url */\r\n    var cloudSyncWebDavUrl: String\r\n        get() = SP.getString(KEY.CLOUD_SYNC_WEBDAV_URL.name, \"\")\r\n        set(value) = SP.putString(KEY.CLOUD_SYNC_WEBDAV_URL.name, value)\r\n\r\n    /** 云同步 webdav 用户名 */\r\n    // CLOUD_SYNC_WEBDAV_USERNAME,\r\n    var cloudSyncWebDavUsername: String\r\n        get() = SP.getString(KEY.CLOUD_SYNC_WEBDAV_USERNAME.name, \"\")\r\n        set(value) = SP.putString(KEY.CLOUD_SYNC_WEBDAV_USERNAME.name, value)\r\n\r\n    /** 云同步 webdav 密码 */\r\n    var cloudSyncWebDavPassword: String\r\n        get() = SP.getString(KEY.CLOUD_SYNC_WEBDAV_PASSWORD.name, \"\")\r\n        set(value) = SP.putString(KEY.CLOUD_SYNC_WEBDAV_PASSWORD.name, value)\r\n\r\n    /** 肥羊 AllInOne 文件路径 */\r\n    var feiyangAllInOneFilePath: String\r\n        get() = SP.getString(KEY.FEIYANG_ALLINONE_FILE_PATH.name, \"\")\r\n        set(value) = SP.putString(KEY.FEIYANG_ALLINONE_FILE_PATH.name, value)\r\n\r\n    enum class UiTimeShowMode(val value: Int, val label: String) {\r\n        /** 隐藏 */\r\n        HIDDEN(0, \"隐藏\"),\r\n\r\n        /** 常显 */\r\n        ALWAYS(1, \"常显\"),\r\n\r\n        /** 整点 */\r\n        EVERY_HOUR(2, \"整点\"),\r\n\r\n        /** 半点 */\r\n        HALF_HOUR(3, \"半点\");\r\n\r\n        companion object {\r\n            fun fromValue(value: Int): UiTimeShowMode {\r\n                return entries.firstOrNull { it.value == value } ?: ALWAYS\r\n            }\r\n        }\r\n    }\r\n\r\n    enum class IptvHybridMode(val value: Int, val label: String) {\r\n        /** 禁用 */\r\n        DISABLE(0, \"禁用\"),\r\n\r\n        /** 直播源优先 */\r\n        IPTV_FIRST(1, \"直播源优先\"),\r\n\r\n        /** 混合优先 */\r\n        HYBRID_FIRST(2, \"混合优先\");\r\n\r\n        companion object {\r\n            fun fromValue(value: Int): IptvHybridMode {\r\n                return entries.firstOrNull { it.value == value } ?: DISABLE\r\n            }\r\n        }\r\n    }\r\n\r\n    enum class VideoPlayerCore(val value: Int, val label: String) {\r\n        /** Media3 */\r\n        MEDIA3(0, \"Media3\"),\r\n\r\n        /** IJK */\r\n        IJK(1, \"IjkPlayer\");\r\n\r\n        companion object {\r\n            fun fromValue(value: Int): VideoPlayerCore {\r\n                return entries.firstOrNull { it.value == value } ?: MEDIA3\r\n            }\r\n        }\r\n    }\r\n\r\n    enum class VideoPlayerRenderMode(val value: Int, val label: String) {\r\n        /** SurfaceView */\r\n        SURFACE_VIEW(0, \"SurfaceView\"),\r\n\r\n        /** TextureView */\r\n        TEXTURE_VIEW(1, \"TextureView\");\r\n\r\n        companion object {\r\n            fun fromValue(value: Int): VideoPlayerRenderMode {\r\n                return entries.firstOrNull { it.value == value } ?: SURFACE_VIEW\r\n            }\r\n        }\r\n    }\r\n\r\n    fun toPartial(): Partial {\r\n        return Partial(\r\n            appBootLaunch = appBootLaunch,\r\n            appPipEnable = appPipEnable,\r\n            appLastLatestVersion = appLastLatestVersion,\r\n            appAgreementAgreed = appAgreementAgreed,\r\n            appStartupScreen = appStartupScreen,\r\n            debugDeveloperMode = debugDeveloperMode,\r\n            debugShowFps = debugShowFps,\r\n            debugShowVideoPlayerMetadata = debugShowVideoPlayerMetadata,\r\n            debugShowLayoutGrids = debugShowLayoutGrids,\r\n            iptvSourceCacheTime = iptvSourceCacheTime,\r\n            iptvSourceCurrent = iptvSourceCurrent,\r\n            iptvSourceList = iptvSourceList,\r\n            iptvChannelGroupHiddenList = iptvChannelGroupHiddenList,\r\n            iptvHybridMode = iptvHybridMode,\r\n            iptvSimilarChannelMerge = iptvSimilarChannelMerge,\r\n            iptvChannelLogoProvider = iptvChannelLogoProvider,\r\n            iptvChannelLogoOverride = iptvChannelLogoOverride,\r\n            iptvChannelFavoriteEnable = iptvChannelFavoriteEnable,\r\n            iptvChannelFavoriteListVisible = iptvChannelFavoriteListVisible,\r\n            iptvChannelFavoriteList = iptvChannelFavoriteList,\r\n            iptvChannelLastPlay = iptvChannelLastPlay,\r\n            iptvChannelLinePlayableHostList = iptvChannelLinePlayableHostList,\r\n            iptvChannelLinePlayableUrlList = iptvChannelLinePlayableUrlList,\r\n            iptvChannelChangeFlip = iptvChannelChangeFlip,\r\n            iptvChannelNoSelectEnable = iptvChannelNoSelectEnable,\r\n            iptvChannelChangeListLoop = iptvChannelChangeListLoop,\r\n            epgEnable = epgEnable,\r\n            epgSourceCurrent = epgSourceCurrent,\r\n            epgSourceList = epgSourceList,\r\n            epgRefreshTimeThreshold = epgRefreshTimeThreshold,\r\n            epgSourceFollowIptv = epgSourceFollowIptv,\r\n            epgChannelReserveList = epgChannelReserveList,\r\n            uiShowEpgProgrammeProgress = uiShowEpgProgrammeProgress,\r\n            uiShowEpgProgrammePermanentProgress = uiShowEpgProgrammePermanentProgress,\r\n            uiShowChannelLogo = uiShowChannelLogo,\r\n            uiShowChannelPreview = uiShowChannelPreview,\r\n            uiUseClassicPanelScreen = uiUseClassicPanelScreen,\r\n            uiDensityScaleRatio = uiDensityScaleRatio,\r\n            uiFontScaleRatio = uiFontScaleRatio,\r\n            uiTimeShowMode = uiTimeShowMode,\r\n            uiFocusOptimize = uiFocusOptimize,\r\n            uiScreenAutoCloseDelay = uiScreenAutoCloseDelay,\r\n            updateForceRemind = updateForceRemind,\r\n            updateChannel = updateChannel,\r\n            videoPlayerCore = videoPlayerCore,\r\n            videoPlayerRenderMode = videoPlayerRenderMode,\r\n            videoPlayerUserAgent = videoPlayerUserAgent,\r\n            videoPlayerHeaders = videoPlayerHeaders,\r\n            videoPlayerLoadTimeout = videoPlayerLoadTimeout,\r\n            videoPlayerDisplayMode = videoPlayerDisplayMode,\r\n            videoPlayerForceAudioSoftDecode = videoPlayerForceAudioSoftDecode,\r\n            videoPlayerStopPreviousMediaItem = videoPlayerStopPreviousMediaItem,\r\n            videoPlayerSkipMultipleFramesOnSameVSync = videoPlayerSkipMultipleFramesOnSameVSync,\r\n            themeAppCurrent = themeAppCurrent,\r\n            cloudSyncAutoPull = cloudSyncAutoPull,\r\n            cloudSyncProvider = cloudSyncProvider,\r\n            cloudSyncGithubGistId = cloudSyncGithubGistId,\r\n            cloudSyncGithubGistToken = cloudSyncGithubGistToken,\r\n            cloudSyncGiteeGistId = cloudSyncGiteeGistId,\r\n            cloudSyncGiteeGistToken = cloudSyncGiteeGistToken,\r\n            cloudSyncNetworkUrl = cloudSyncNetworkUrl,\r\n            cloudSyncLocalFilePath = cloudSyncLocalFilePath,\r\n            cloudSyncWebDavUrl = cloudSyncWebDavUrl,\r\n            cloudSyncWebDavUsername = cloudSyncWebDavUsername,\r\n            cloudSyncWebDavPassword = cloudSyncWebDavPassword,\r\n            feiyangAllInOneFilePath = feiyangAllInOneFilePath,\r\n        )\r\n    }\r\n\r\n    fun fromPartial(configs: Partial) {\r\n        configs.appBootLaunch?.let { appBootLaunch = it }\r\n        configs.appPipEnable?.let { appPipEnable = it }\r\n        configs.appLastLatestVersion?.let { appLastLatestVersion = it }\r\n        configs.appAgreementAgreed?.let { appAgreementAgreed = it }\r\n        configs.appStartupScreen?.let { appStartupScreen = it }\r\n        configs.debugDeveloperMode?.let { debugDeveloperMode = it }\r\n        configs.debugShowFps?.let { debugShowFps = it }\r\n        configs.debugShowVideoPlayerMetadata?.let { debugShowVideoPlayerMetadata = it }\r\n        configs.debugShowLayoutGrids?.let { debugShowLayoutGrids = it }\r\n        configs.iptvSourceCacheTime?.let { iptvSourceCacheTime = it }\r\n        configs.iptvSourceCurrent?.let { iptvSourceCurrent = it }\r\n        configs.iptvSourceList?.let { iptvSourceList = it }\r\n        configs.iptvChannelGroupHiddenList?.let { iptvChannelGroupHiddenList = it }\r\n        configs.iptvHybridMode?.let { iptvHybridMode = it }\r\n        configs.iptvSimilarChannelMerge?.let { iptvSimilarChannelMerge = it }\r\n        configs.iptvChannelLogoProvider?.let { iptvChannelLogoProvider = it }\r\n        configs.iptvChannelLogoOverride?.let { iptvChannelLogoOverride = it }\r\n        configs.iptvChannelFavoriteEnable?.let { iptvChannelFavoriteEnable = it }\r\n        configs.iptvChannelFavoriteListVisible?.let { iptvChannelFavoriteListVisible = it }\r\n        configs.iptvChannelFavoriteList?.let { iptvChannelFavoriteList = it }\r\n        configs.iptvChannelLastPlay?.let { iptvChannelLastPlay = it }\r\n        configs.iptvChannelLinePlayableHostList?.let { iptvChannelLinePlayableHostList = it }\r\n        configs.iptvChannelLinePlayableUrlList?.let { iptvChannelLinePlayableUrlList = it }\r\n        configs.iptvChannelChangeFlip?.let { iptvChannelChangeFlip = it }\r\n        configs.iptvChannelNoSelectEnable?.let { iptvChannelNoSelectEnable = it }\r\n        configs.iptvChannelChangeListLoop?.let { iptvChannelChangeListLoop = it }\r\n        configs.epgEnable?.let { epgEnable = it }\r\n        configs.epgSourceCurrent?.let { epgSourceCurrent = it }\r\n        configs.epgSourceList?.let { epgSourceList = it }\r\n        configs.epgRefreshTimeThreshold?.let { epgRefreshTimeThreshold = it }\r\n        configs.epgSourceFollowIptv?.let { epgSourceFollowIptv = it }\r\n        configs.epgChannelReserveList?.let { epgChannelReserveList = it }\r\n        configs.uiShowEpgProgrammeProgress?.let { uiShowEpgProgrammeProgress = it }\r\n        configs.uiShowEpgProgrammePermanentProgress?.let {\r\n            uiShowEpgProgrammePermanentProgress = it\r\n        }\r\n        configs.uiShowChannelLogo?.let { uiShowChannelLogo = it }\r\n        configs.uiShowChannelPreview?.let { uiShowChannelPreview = it }\r\n        configs.uiUseClassicPanelScreen?.let { uiUseClassicPanelScreen = it }\r\n        configs.uiDensityScaleRatio?.let { uiDensityScaleRatio = it }\r\n        configs.uiFontScaleRatio?.let { uiFontScaleRatio = it }\r\n        configs.uiTimeShowMode?.let { uiTimeShowMode = it }\r\n        configs.uiFocusOptimize?.let { uiFocusOptimize = it }\r\n        configs.uiScreenAutoCloseDelay?.let { uiScreenAutoCloseDelay = it }\r\n        configs.updateForceRemind?.let { updateForceRemind = it }\r\n        configs.updateChannel?.let { updateChannel = it }\r\n        configs.videoPlayerCore?.let { videoPlayerCore = it }\r\n        configs.videoPlayerRenderMode?.let { videoPlayerRenderMode = it }\r\n        configs.videoPlayerUserAgent?.let { videoPlayerUserAgent = it }\r\n        configs.videoPlayerHeaders?.let { videoPlayerHeaders = it }\r\n        configs.videoPlayerLoadTimeout?.let { videoPlayerLoadTimeout = it }\r\n        configs.videoPlayerDisplayMode?.let { videoPlayerDisplayMode = it }\r\n        configs.videoPlayerForceAudioSoftDecode?.let { videoPlayerForceAudioSoftDecode = it }\r\n        configs.videoPlayerStopPreviousMediaItem?.let { videoPlayerStopPreviousMediaItem = it }\r\n        configs.themeAppCurrent?.let { themeAppCurrent = it }\r\n        configs.cloudSyncAutoPull?.let { cloudSyncAutoPull = it }\r\n        configs.cloudSyncProvider?.let { cloudSyncProvider = it }\r\n        configs.cloudSyncGithubGistId?.let { cloudSyncGithubGistId = it }\r\n        configs.cloudSyncGithubGistToken?.let { cloudSyncGithubGistToken = it }\r\n        configs.cloudSyncGiteeGistId?.let { cloudSyncGiteeGistId = it }\r\n        configs.cloudSyncGiteeGistToken?.let { cloudSyncGiteeGistToken = it }\r\n        configs.cloudSyncNetworkUrl?.let { cloudSyncNetworkUrl = it }\r\n        configs.cloudSyncLocalFilePath?.let { cloudSyncLocalFilePath = it }\r\n        configs.cloudSyncWebDavUrl?.let { cloudSyncWebDavUrl = it }\r\n        configs.cloudSyncWebDavUsername?.let { cloudSyncWebDavUsername = it }\r\n        configs.cloudSyncWebDavPassword?.let { cloudSyncWebDavPassword = it }\r\n        configs.feiyangAllInOneFilePath?.let { feiyangAllInOneFilePath = it }\r\n    }\r\n\r\n    @Serializable\r\n    data class Partial(\r\n        val appBootLaunch: Boolean? = null,\r\n        val appPipEnable: Boolean? = null,\r\n        val appLastLatestVersion: String? = null,\r\n        val appAgreementAgreed: Boolean? = null,\r\n        val appStartupScreen: String? = null,\r\n        val debugDeveloperMode: Boolean? = null,\r\n        val debugShowFps: Boolean? = null,\r\n        val debugShowVideoPlayerMetadata: Boolean? = null,\r\n        val debugShowLayoutGrids: Boolean? = null,\r\n        val iptvSourceCacheTime: Long? = null,\r\n        val iptvSourceCurrent: IptvSource? = null,\r\n        val iptvSourceList: IptvSourceList? = null,\r\n        val iptvChannelGroupHiddenList: Set<String>? = null,\r\n        val iptvHybridMode: IptvHybridMode? = null,\r\n        val iptvSimilarChannelMerge: Boolean? = null,\r\n        val iptvChannelLogoProvider: String? = null,\r\n        val iptvChannelLogoOverride: Boolean? = null,\r\n        val iptvChannelFavoriteEnable: Boolean? = null,\r\n        val iptvChannelFavoriteListVisible: Boolean? = null,\r\n        val iptvChannelFavoriteList: ChannelFavoriteList? = null,\r\n        val iptvChannelLastPlay: Channel? = null,\r\n        val iptvChannelLinePlayableHostList: Set<String>? = null,\r\n        val iptvChannelLinePlayableUrlList: Set<String>? = null,\r\n        val iptvChannelChangeFlip: Boolean? = null,\r\n        val iptvChannelNoSelectEnable: Boolean? = null,\r\n        val iptvChannelChangeListLoop: Boolean? = null,\r\n        val epgEnable: Boolean? = null,\r\n        val epgSourceCurrent: EpgSource? = null,\r\n        val epgSourceList: EpgSourceList? = null,\r\n        val epgRefreshTimeThreshold: Int? = null,\r\n        val epgSourceFollowIptv: Boolean? = null,\r\n        val epgChannelReserveList: EpgProgrammeReserveList? = null,\r\n        val uiShowEpgProgrammeProgress: Boolean? = null,\r\n        val uiShowEpgProgrammePermanentProgress: Boolean? = null,\r\n        val uiShowChannelLogo: Boolean? = null,\r\n        val uiShowChannelPreview: Boolean? = null,\r\n        val uiUseClassicPanelScreen: Boolean? = null,\r\n        val uiDensityScaleRatio: Float? = null,\r\n        val uiFontScaleRatio: Float? = null,\r\n        val uiTimeShowMode: UiTimeShowMode? = null,\r\n        val uiFocusOptimize: Boolean? = null,\r\n        val uiScreenAutoCloseDelay: Long? = null,\r\n        val updateForceRemind: Boolean? = null,\r\n        val updateChannel: String? = null,\r\n        val videoPlayerCore: VideoPlayerCore? = null,\r\n        val videoPlayerRenderMode: VideoPlayerRenderMode? = null,\r\n        val videoPlayerUserAgent: String? = null,\r\n        val videoPlayerHeaders: String? = null,\r\n        val videoPlayerLoadTimeout: Long? = null,\r\n        val videoPlayerDisplayMode: VideoPlayerDisplayMode? = null,\r\n        val videoPlayerForceAudioSoftDecode: Boolean? = null,\r\n        val videoPlayerStopPreviousMediaItem: Boolean? = null,\r\n        val videoPlayerSkipMultipleFramesOnSameVSync: Boolean? = null,\r\n        val themeAppCurrent: AppThemeDef? = null,\r\n        val cloudSyncAutoPull: Boolean? = null,\r\n        val cloudSyncProvider: CloudSyncProvider? = null,\r\n        val cloudSyncGithubGistId: String? = null,\r\n        val cloudSyncGithubGistToken: String? = null,\r\n        val cloudSyncGiteeGistId: String? = null,\r\n        val cloudSyncGiteeGistToken: String? = null,\r\n        val cloudSyncNetworkUrl: String? = null,\r\n        val cloudSyncLocalFilePath: String? = null,\r\n        val cloudSyncWebDavUrl: String? = null,\r\n        val cloudSyncWebDavUsername: String? = null,\r\n        val cloudSyncWebDavPassword: String? = null,\r\n        val feiyangAllInOneFilePath: String? = null,\r\n    ) {\r\n        fun desensitized() = copy(\r\n            cloudSyncAutoPull = null,\r\n            cloudSyncProvider = null,\r\n            cloudSyncGithubGistId = null,\r\n            cloudSyncGithubGistToken = null,\r\n            cloudSyncGiteeGistId = null,\r\n            cloudSyncGiteeGistToken = null,\r\n            cloudSyncNetworkUrl = null,\r\n            cloudSyncLocalFilePath = null,\r\n            cloudSyncWebDavUrl = null,\r\n            cloudSyncWebDavUsername = null,\r\n            cloudSyncWebDavPassword = null,\r\n            iptvChannelLastPlay = null,\r\n            iptvChannelLinePlayableHostList = null,\r\n            iptvChannelLinePlayableUrlList = null,\r\n        )\r\n    }\r\n}
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/tv/src/main/java/top/yogiczy/mytv/tv/ui/utils/Configs.kt b/tv/src/main/java/top/yogiczy/mytv/tv/ui/utils/Configs.kt
--- a/tv/src/main/java/top/yogiczy/mytv/tv/ui/utils/Configs.kt	(revision ****************************************)
+++ b/tv/src/main/java/top/yogiczy/mytv/tv/ui/utils/Configs.kt	(date 1755500641841)
@@ -225,9 +225,6 @@
 
         /** 云同步 webdav 密码 */
         CLOUD_SYNC_WEBDAV_PASSWORD,
-
-        /** 肥羊 AllInOne 文件路径 */
-        FEIYANG_ALLINONE_FILE_PATH,
     }
 
     /** ==================== 应用 ==================== */
@@ -559,13 +556,13 @@
     /** ==================== 云同步 ==================== */
     /** 云同步 自动拉取 */
     var cloudSyncAutoPull: Boolean
-        get() = SP.getBoolean(KEY.CLOUD_SYNC_AUTO_PULL.name, false)
+        get() = SP.getBoolean(KEY.CLOUD_SYNC_AUTO_PULL.name, true)
         set(value) = SP.putBoolean(KEY.CLOUD_SYNC_AUTO_PULL.name, value)
 
     /** 云同步 提供商 */
     var cloudSyncProvider: CloudSyncProvider
         get() = CloudSyncProvider.fromValue(
-            SP.getInt(KEY.CLOUD_SYNC_PROVIDER.name, CloudSyncProvider.GITHUB_GIST.value)
+            SP.getInt(KEY.CLOUD_SYNC_PROVIDER.name, CloudSyncProvider.NETWORK_URL.value)
         )
         set(value) = SP.putInt(KEY.CLOUD_SYNC_PROVIDER.name, value.value)
 
@@ -614,12 +611,6 @@
     var cloudSyncWebDavPassword: String
         get() = SP.getString(KEY.CLOUD_SYNC_WEBDAV_PASSWORD.name, "")
         set(value) = SP.putString(KEY.CLOUD_SYNC_WEBDAV_PASSWORD.name, value)
-
-    /** 肥羊 AllInOne 文件路径 */
-    var feiyangAllInOneFilePath: String
-        get() = SP.getString(KEY.FEIYANG_ALLINONE_FILE_PATH.name, "")
-        set(value) = SP.putString(KEY.FEIYANG_ALLINONE_FILE_PATH.name, value)
-
     enum class UiTimeShowMode(val value: Int, val label: String) {
         /** 隐藏 */
         HIDDEN(0, "隐藏"),
@@ -752,7 +743,6 @@
             cloudSyncWebDavUrl = cloudSyncWebDavUrl,
             cloudSyncWebDavUsername = cloudSyncWebDavUsername,
             cloudSyncWebDavPassword = cloudSyncWebDavPassword,
-            feiyangAllInOneFilePath = feiyangAllInOneFilePath,
         )
     }
 
@@ -823,7 +813,6 @@
         configs.cloudSyncWebDavUrl?.let { cloudSyncWebDavUrl = it }
         configs.cloudSyncWebDavUsername?.let { cloudSyncWebDavUsername = it }
         configs.cloudSyncWebDavPassword?.let { cloudSyncWebDavPassword = it }
-        configs.feiyangAllInOneFilePath?.let { feiyangAllInOneFilePath = it }
     }
 
     @Serializable
@@ -893,7 +882,6 @@
         val cloudSyncWebDavUrl: String? = null,
         val cloudSyncWebDavUsername: String? = null,
         val cloudSyncWebDavPassword: String? = null,
-        val feiyangAllInOneFilePath: String? = null,
     ) {
         fun desensitized() = copy(
             cloudSyncAutoPull = null,
Index: tv/build.gradle.kts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import com.android.build.gradle.internal.dsl.BaseAppModuleExtension\r\nimport java.io.FileInputStream\r\nimport java.util.Properties\r\n\r\nplugins {\r\n    alias(libs.plugins.android.application)\r\n    alias(libs.plugins.kotlin.android)\r\n    alias(libs.plugins.compose)\r\n    alias(libs.plugins.kotlin.serialization)\r\n    alias(libs.plugins.sentry.android.gradle)\r\n}\r\n\r\nandroid {\r\n    @Suppress(\"UNCHECKED_CAST\")\r\n    apply(extra[\"appConfig\"] as BaseAppModuleExtension.() -> Unit)\r\n\r\n    namespace = \"top.yogiczy.mytv.tv\"\r\n    compileSdk = libs.versions.compileSdk.get().toInt()\r\n\r\n    defaultConfig {\r\n        applicationId = \"top.yogiczy.slcs.tv\"\r\n        minSdk = libs.versions.minSdk.get().toInt()\r\n        targetSdk = libs.versions.targetSdk.get().toInt()\r\n        versionCode = 2\r\n        versionName = \"3.3.9\"\r\n        vectorDrawables {\r\n            useSupportLibrary = true\r\n        }\r\n\r\n        buildConfigField(\"String\", \"SENTRY_DSN\", \"\\\"${getProperty(\"sentry.dsn\") ?: \"\"}\\\"\")\r\n    }\r\n\r\n    buildTypes {\r\n        release {\r\n            isMinifyEnabled = true\r\n            isShrinkResources = true\r\n            proguardFiles(\r\n                getDefaultProguardFile(\"proguard-android-optimize.txt\"),\r\n                \"proguard-rules.pro\",\r\n            )\r\n            signingConfig = signingConfigs.getByName(\"release\")\r\n\r\n            ndk {\r\n                abiFilters.addAll(listOf(\"armeabi-v7a\", \"arm64-v8a\"))\r\n            }\r\n        }\r\n    }\r\n\r\n    sourceSets {\r\n        getByName(\"main\") {\r\n            jniLibs.srcDirs(\"jniLibs\")\r\n        }\r\n    }\r\n\r\n    compileOptions {\r\n        isCoreLibraryDesugaringEnabled = true\r\n        sourceCompatibility = JavaVersion.VERSION_11\r\n        targetCompatibility = JavaVersion.VERSION_11\r\n    }\r\n\r\n    kotlinOptions {\r\n        jvmTarget = \"11\"\r\n    }\r\n\r\n    buildFeatures {\r\n        compose = true\r\n        buildConfig = true\r\n    }\r\n\r\n    packaging {\r\n        resources {\r\n            excludes += \"/META-INF/{AL2.0,LGPL2.1}\"\r\n        }\r\n    }\r\n\r\n    flavorDimensions += listOf(\"version\")\r\n    productFlavors {\r\n        create(\"original\") {\r\n            dimension = \"version\"\r\n        }\r\n\r\n        create(\"disguised\") {\r\n            dimension = \"version\"\r\n            applicationId = \"com.chinablue.tv\"\r\n        }\r\n    }\r\n\r\n    // splits {\r\n    //     abi {\r\n    //         isEnable = true\r\n    //         isUniversalApk = false\r\n    //         reset()\r\n    //         // noinspection ChromeOsAbiSupport\r\n    //         include(\"arm64-v8a\")\r\n    //     }\r\n    // }\r\n}\r\n\r\ndependencies {\r\n    implementation(libs.androidx.core.ktx)\r\n    implementation(libs.androidx.appcompat)\r\n    implementation(platform(libs.androidx.compose.bom))\r\n    implementation(libs.androidx.ui.tooling.preview)\r\n    implementation(libs.androidx.compose.foundation.base)\r\n    implementation(libs.androidx.tv.material)\r\n    implementation(libs.androidx.lifecycle.runtime.ktx)\r\n    implementation(libs.androidx.lifecycle.viewmodel.compose)\r\n    implementation(libs.androidx.activity.compose)\r\n    implementation(libs.androidx.navigation.compose)\r\n\r\n    implementation(libs.kotlinx.serialization)\r\n    implementation(libs.kotlinx.collections.immutable)\r\n    implementation(libs.androidx.material.icons.extended)\r\n\r\n    // 播放器\r\n    val mediaSettingsFile = file(\"../../media/core_settings.gradle\")\r\n    if (mediaSettingsFile.exists()) {\r\n        implementation(project(\":media3:lib-exoplayer\"))\r\n        implementation(project(\":media3:lib-exoplayer-hls\"))\r\n        implementation(project(\":media3:lib-exoplayer-rtsp\"))\r\n        implementation(project(\":media3:lib-exoplayer-dash\"))\r\n        implementation(project(\":media3:lib-ui\"))\r\n    } else {\r\n        implementation(libs.androidx.media3.exoplayer)\r\n        implementation(libs.androidx.media3.exoplayer.hls)\r\n        implementation(libs.androidx.media3.exoplayer.rtsp)\r\n        implementation(libs.androidx.media3.exoplayer.dash)\r\n        implementation(libs.androidx.media3.ui)\r\n    }\r\n\r\n    implementation(\"com.github.CarGuo.GSYVideoPlayer:gsyvideoplayer-java:v10.0.0\")\r\n    implementation(\"com.github.CarGuo.GSYVideoPlayer:gsyvideoplayer-ex_so:v10.0.0\")\r\n\r\n    // 二维码\r\n    implementation(libs.qrose)\r\n\r\n    implementation(libs.coil.compose)\r\n    implementation(libs.coil.svg)\r\n\r\n    implementation(libs.okhttp)\r\n    implementation(libs.androidasync)\r\n\r\n    implementation(libs.tinypinyin)\r\n\r\n    implementation(project(\":core:data\"))\r\n    implementation(project(\":core:designsystem\"))\r\n    implementation(project(\":core:util\"))\r\n    implementation(project(\":allinone\"))\r\n    // implementation(project(\":tbsx5\"))\r\n\r\n    implementation(fileTree(mapOf(\"dir\" to \"libs\", \"include\" to listOf(\"*.aar\", \"*.jar\"))))\r\n\r\n    androidTestImplementation(platform(libs.androidx.compose.bom))\r\n    androidTestImplementation(libs.androidx.ui.test.junit4)\r\n    debugImplementation(libs.androidx.ui.tooling)\r\n    debugImplementation(libs.androidx.ui.test.manifest)\r\n    coreLibraryDesugaring(libs.desugar.jdk.libs)\r\n}\r\n\r\nsentry {\r\n    org.set(\"yogiczy\")\r\n    projectName.set(\"mytv-android\")\r\n    authToken.set(getProperty(\"sentry.auth_token\") ?: System.getenv(\"SENTRY_AUTH_TOKEN\"))\r\n    ignoredBuildTypes.set(setOf(\"debug\"))\r\n    autoUploadProguardMapping = false\r\n}\r\n\r\nfun getProperty(key: String): String? {\r\n    val propertiesFile = rootProject.file(\"local.properties\")\r\n    if (propertiesFile.exists()) {\r\n        val properties = Properties()\r\n        properties.load(FileInputStream(propertiesFile))\r\n\r\n        return properties.getProperty(key)\r\n    }\r\n\r\n    return null\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/tv/build.gradle.kts b/tv/build.gradle.kts
--- a/tv/build.gradle.kts	(revision ****************************************)
+++ b/tv/build.gradle.kts	(date 1755500641841)
@@ -28,6 +28,8 @@
         }
 
         buildConfigField("String", "SENTRY_DSN", "\"${getProperty("sentry.dsn") ?: ""}\"")
+        // 默认云同步拉取地址（可留空，运行时允许设置覆盖）
+        buildConfigField("String", "DEFAULT_CLOUD_SYNC_URL", "\"${getProperty("cloudsync.default_url") ?: ""}\"")
     }
 
     buildTypes {
@@ -145,7 +147,6 @@
     implementation(project(":core:data"))
     implementation(project(":core:designsystem"))
     implementation(project(":core:util"))
-    implementation(project(":allinone"))
     // implementation(project(":tbsx5"))
 
     implementation(fileTree(mapOf("dir" to "libs", "include" to listOf("*.aar", "*.jar"))))
Index: tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/videoplayer/player/Media3VideoPlayer.kt
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package top.yogiczy.mytv.tv.ui.screensold.videoplayer.player\r\n\r\nimport android.content.Context\r\nimport android.net.Uri\r\nimport android.util.Base64\r\nimport android.view.SurfaceView\r\nimport android.view.TextureView\r\nimport androidx.annotation.OptIn\r\nimport androidx.media3.common.C\r\nimport androidx.media3.common.Format\r\nimport androidx.media3.common.MediaItem\r\nimport androidx.media3.common.MimeTypes\r\nimport androidx.media3.common.Player\r\nimport androidx.media3.common.TrackGroup\r\nimport androidx.media3.common.TrackSelectionOverride\r\nimport androidx.media3.common.Tracks\r\nimport androidx.media3.common.VideoSize\r\nimport androidx.media3.common.text.Cue\r\nimport androidx.media3.common.text.CueGroup\r\nimport androidx.media3.common.util.UnstableApi\r\nimport androidx.media3.common.util.Util\r\nimport androidx.media3.datasource.DefaultDataSource\r\nimport androidx.media3.datasource.DefaultHttpDataSource\r\nimport androidx.media3.exoplayer.DecoderReuseEvaluation\r\nimport androidx.media3.exoplayer.DefaultRenderersFactory\r\nimport androidx.media3.exoplayer.ExoPlayer\r\nimport androidx.media3.exoplayer.analytics.AnalyticsListener\r\nimport androidx.media3.exoplayer.dash.DashMediaSource\r\nimport androidx.media3.exoplayer.drm.DefaultDrmSessionManager\r\nimport androidx.media3.exoplayer.drm.FrameworkMediaDrm\r\nimport androidx.media3.exoplayer.drm.LocalMediaDrmCallback\r\nimport androidx.media3.exoplayer.hls.HlsMediaSource\r\nimport androidx.media3.exoplayer.rtsp.RtspMediaSource\r\nimport androidx.media3.exoplayer.source.MediaSource\r\nimport androidx.media3.exoplayer.source.ProgressiveMediaSource\r\nimport androidx.media3.exoplayer.trackselection.DefaultTrackSelector\r\nimport androidx.media3.exoplayer.util.EventLogger\r\nimport androidx.media3.exoplayer.video.MediaCodecVideoRenderer\r\nimport com.google.common.collect.ImmutableList\r\nimport kotlinx.coroutines.CoroutineScope\r\nimport kotlinx.coroutines.Job\r\nimport kotlinx.coroutines.delay\r\nimport kotlinx.coroutines.launch\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelLine\r\nimport top.yogiczy.mytv.core.util.utils.toHeaders\r\nimport top.yogiczy.mytv.tv.ui.utils.Configs\r\n\r\n@OptIn(UnstableApi::class)\r\nclass Media3VideoPlayer(\r\n    private val context: Context,\r\n    private val coroutineScope: CoroutineScope,\r\n) : VideoPlayer(coroutineScope) {\r\n\r\n    private var videoPlayer = getPlayer()\r\n\r\n    private var softDecode: Boolean? = null\r\n    private var surfaceView: SurfaceView? = null\r\n    private var textureView: TextureView? = null\r\n\r\n    private var currentChannelLine = ChannelLine()\r\n    private val contentTypeAttempts = mutableMapOf<Int, Boolean>()\r\n    private var updatePositionJob: Job? = null\r\n\r\n    private val onCuesListeners = mutableListOf<(ImmutableList<Cue>) -> Unit>()\r\n\r\n    private fun triggerCues(cues: ImmutableList<Cue>) {\r\n        onCuesListeners.forEach { it(cues) }\r\n    }\r\n\r\n    fun onCues(listener: (ImmutableList<Cue>) -> Unit) {\r\n        onCuesListeners.add(listener)\r\n    }\r\n\r\n    private fun getPlayer(): ExoPlayer {\r\n        val renderersFactory =\r\n            DefaultRenderersFactory(context)\r\n                .setExtensionRendererMode(\r\n                    if (softDecode ?: Configs.videoPlayerForceAudioSoftDecode)\r\n                        DefaultRenderersFactory.EXTENSION_RENDERER_MODE_PREFER\r\n                    else DefaultRenderersFactory.EXTENSION_RENDERER_MODE_ON\r\n                )\r\n                .setEnableDecoderFallback(true)\r\n\r\n        val trackSelector = DefaultTrackSelector(context).apply {\r\n            parameters = buildUponParameters()\r\n                .setTrackTypeDisabled(C.TRACK_TYPE_VIDEO, false)\r\n                .setTrackTypeDisabled(C.TRACK_TYPE_AUDIO, false)\r\n                .setTrackTypeDisabled(C.TRACK_TYPE_TEXT, false)\r\n                .setMaxVideoSize(Integer.MAX_VALUE, Integer.MAX_VALUE)\r\n                .setForceHighestSupportedBitrate(true)\r\n                .setPreferredTextLanguages(\"zh\")\r\n                .build()\r\n        }\r\n\r\n        MediaCodecVideoRenderer.skipMultipleFramesOnSameVsync =\r\n            Configs.videoPlayerSkipMultipleFramesOnSameVSync\r\n        return ExoPlayer.Builder(context)\r\n            .setRenderersFactory(renderersFactory)\r\n            .setTrackSelector(trackSelector)\r\n            .build()\r\n            .apply { playWhenReady = true }\r\n    }\r\n\r\n    private fun reInitPlayer() {\r\n        onCuesListeners.clear()\r\n        videoPlayer.removeListener(playerListener)\r\n        videoPlayer.removeAnalyticsListener(metadataListener)\r\n        videoPlayer.removeAnalyticsListener(eventLogger)\r\n        videoPlayer.stop()\r\n        videoPlayer.release()\r\n\r\n        videoPlayer = getPlayer()\r\n\r\n        videoPlayer.addListener(playerListener)\r\n        videoPlayer.addAnalyticsListener(metadataListener)\r\n        videoPlayer.addAnalyticsListener(eventLogger)\r\n\r\n        surfaceView?.let { setVideoSurfaceView(it) }\r\n        textureView?.let { setVideoTextureView(it) }\r\n        prepare()\r\n    }\r\n\r\n    private fun getDataSourceFactory(): DefaultDataSource.Factory {\r\n        return DefaultDataSource.Factory(\r\n            context,\r\n            DefaultHttpDataSource.Factory().apply {\r\n                setUserAgent(currentChannelLine.httpUserAgent ?: Configs.videoPlayerUserAgent)\r\n                setDefaultRequestProperties(Configs.videoPlayerHeaders.toHeaders())\r\n                setConnectTimeoutMs(Configs.videoPlayerLoadTimeout.toInt())\r\n                setReadTimeoutMs(Configs.videoPlayerLoadTimeout.toInt())\r\n                setKeepPostFor302Redirects(true)\r\n                setAllowCrossProtocolRedirects(true)\r\n            },\r\n        )\r\n    }\r\n\r\n    private fun getMediaSource(contentType: Int? = null): MediaSource? {\r\n        val uri = Uri.parse(currentChannelLine.playableUrl)\r\n        val mediaItem = MediaItem.fromUri(uri)\r\n\r\n        var contentTypeForce = contentType\r\n\r\n        if (uri.toString().startsWith(\"rtp://\")) {\r\n            contentTypeForce = C.CONTENT_TYPE_RTSP\r\n        }\r\n\r\n        if (currentChannelLine.manifestType == \"mpd\") {\r\n            contentTypeForce = C.CONTENT_TYPE_DASH\r\n        }\r\n\r\n        val dataSourceFactory = getDataSourceFactory()\r\n        return when (contentTypeForce ?: Util.inferContentType(uri)) {\r\n            C.CONTENT_TYPE_HLS -> {\r\n                HlsMediaSource.Factory(dataSourceFactory).createMediaSource(mediaItem)\r\n            }\r\n\r\n            C.CONTENT_TYPE_DASH -> {\r\n                DashMediaSource.Factory(dataSourceFactory)\r\n                    .apply {\r\n                        if (!(currentChannelLine.manifestType == \"mpd\" && currentChannelLine.licenseType == \"clearkey\" && currentChannelLine.licenseKey != null))\r\n                            return@apply\r\n\r\n                        runCatching {\r\n                            val (drmKeyId, drmKey) = currentChannelLine.licenseKey!!.split(\":\")\r\n                            val encodedDrmKey = Base64.encodeToString(\r\n                                drmKey.chunked(2).map { it.toInt(16).toByte() }.toByteArray(),\r\n                                Base64.URL_SAFE or Base64.NO_PADDING or Base64.NO_WRAP\r\n                            )\r\n                            val encodedDrmKeyId = Base64.encodeToString(\r\n                                drmKeyId.chunked(2).map { it.toInt(16).toByte() }.toByteArray(),\r\n                                Base64.URL_SAFE or Base64.NO_PADDING or Base64.NO_WRAP\r\n                            )\r\n                            val drmBody =\r\n                                \"{\\\"keys\\\":[{\\\"kty\\\":\\\"oct\\\",\\\"k\\\":\\\"${encodedDrmKey}\\\",\\\"kid\\\":\\\"${encodedDrmKeyId}\\\"}],\\\"type\\\":\\\"temporary\\\"}\"\r\n\r\n                            val drmCallback = LocalMediaDrmCallback(drmBody.toByteArray())\r\n                            val drmSessionManager = DefaultDrmSessionManager.Builder()\r\n                                .setMultiSession(true)\r\n                                .setUuidAndExoMediaDrmProvider(\r\n                                    C.CLEARKEY_UUID,\r\n                                    FrameworkMediaDrm.DEFAULT_PROVIDER\r\n                                )\r\n                                .build(drmCallback)\r\n\r\n                            setDrmSessionManagerProvider { drmSessionManager }\r\n                        }\r\n                            .onFailure {\r\n                                triggerError(\r\n                                    PlaybackException(\r\n                                        \"MEDIA3_ERROR_DRM_LICENSE_EXPIRED\",\r\n                                        androidx.media3.common.PlaybackException.ERROR_CODE_DRM_LICENSE_EXPIRED\r\n                                    )\r\n                                )\r\n                            }\r\n                    }\r\n                    .createMediaSource(mediaItem)\r\n            }\r\n\r\n            C.CONTENT_TYPE_RTSP -> {\r\n                RtspMediaSource.Factory().createMediaSource(mediaItem)\r\n            }\r\n\r\n            C.CONTENT_TYPE_OTHER -> {\r\n                ProgressiveMediaSource.Factory(dataSourceFactory).createMediaSource(mediaItem)\r\n            }\r\n\r\n            else -> {\r\n                triggerError(PlaybackException.UNSUPPORTED_TYPE)\r\n                null\r\n            }\r\n        }\r\n    }\r\n\r\n    private fun prepare(contentType: Int? = null) {\r\n        val uri = Uri.parse(currentChannelLine.playableUrl)\r\n        val mediaSource = getMediaSource(contentType)\r\n\r\n        if (mediaSource != null) {\r\n            contentTypeAttempts[contentType ?: Util.inferContentType(uri)] = true\r\n            videoPlayer.setMediaSource(mediaSource)\r\n            videoPlayer.prepare()\r\n            videoPlayer.play()\r\n            triggerPrepared()\r\n        }\r\n        updatePositionJob?.cancel()\r\n        updatePositionJob = null\r\n    }\r\n\r\n    private val playerListener = object : Player.Listener {\r\n        override fun onVideoSizeChanged(videoSize: VideoSize) {\r\n            triggerResolution(videoSize.width, videoSize.height)\r\n        }\r\n\r\n        override fun onPlayerError(ex: androidx.media3.common.PlaybackException) {\r\n            when (ex.errorCode) {\r\n                // 如果是直播加载位置错误，尝试重新播放\r\n                androidx.media3.common.PlaybackException.ERROR_CODE_BEHIND_LIVE_WINDOW,\r\n                androidx.media3.common.PlaybackException.ERROR_CODE_DECODING_FAILED,\r\n                androidx.media3.common.PlaybackException.ERROR_CODE_IO_UNSPECIFIED -> {\r\n                    videoPlayer.seekToDefaultPosition()\r\n                    videoPlayer.prepare()\r\n                }\r\n\r\n                // 当解析容器不支持时，尝试使用其他解析容器\r\n                androidx.media3.common.PlaybackException.ERROR_CODE_PARSING_CONTAINER_UNSUPPORTED -> {\r\n                    videoPlayer.currentMediaItem?.localConfiguration?.uri?.let {\r\n                        if (contentTypeAttempts[C.CONTENT_TYPE_HLS] != true) {\r\n                            prepare(C.CONTENT_TYPE_HLS)\r\n                        } else if (contentTypeAttempts[C.CONTENT_TYPE_DASH] != true) {\r\n                            prepare(C.CONTENT_TYPE_DASH)\r\n                        } else if (contentTypeAttempts[C.CONTENT_TYPE_RTSP] != true) {\r\n                            prepare(C.CONTENT_TYPE_RTSP)\r\n                        } else if (contentTypeAttempts[C.CONTENT_TYPE_OTHER] != true) {\r\n                            prepare(C.CONTENT_TYPE_OTHER)\r\n                        } else {\r\n                            triggerError(PlaybackException.UNSUPPORTED_TYPE)\r\n                        }\r\n                    }\r\n                }\r\n\r\n                androidx.media3.common.PlaybackException.ERROR_CODE_DECODER_INIT_FAILED -> {\r\n                    if (softDecode == true) {\r\n                        triggerError(\r\n                            PlaybackException(\r\n                                ex.errorCodeName.replace(\"ERROR_CODE\", \"MEDIA3_ERROR\"),\r\n                                ex.errorCode\r\n                            )\r\n                        )\r\n                    } else {\r\n                        softDecode = true\r\n                        reInitPlayer()\r\n                    }\r\n                }\r\n\r\n                else -> {\r\n                    triggerError(\r\n                        PlaybackException(\r\n                            ex.errorCodeName.replace(\"ERROR_CODE\", \"MEDIA3_ERROR\"),\r\n                            ex.errorCode\r\n                        )\r\n                    )\r\n                }\r\n            }\r\n        }\r\n\r\n        override fun onPlaybackStateChanged(playbackState: Int) {\r\n            if (playbackState == Player.STATE_BUFFERING) {\r\n                triggerError(null)\r\n                triggerBuffering(true)\r\n            } else if (playbackState == Player.STATE_READY) {\r\n                triggerReady()\r\n                triggerDuration(videoPlayer.duration)\r\n\r\n                updatePositionJob?.cancel()\r\n                updatePositionJob = coroutineScope.launch {\r\n                    while (true) {\r\n                        val livePosition =\r\n                            System.currentTimeMillis() - videoPlayer.currentLiveOffset\r\n\r\n                        triggerCurrentPosition(if (livePosition > 0) livePosition else videoPlayer.currentPosition)\r\n                        delay(500)\r\n                    }\r\n                }\r\n            }\r\n\r\n            if (playbackState != Player.STATE_BUFFERING) {\r\n                triggerBuffering(false)\r\n            }\r\n\r\n            if (playbackState == Player.STATE_ENDED) {\r\n                videoPlayer.seekToDefaultPosition()\r\n                videoPlayer.prepare()\r\n            }\r\n        }\r\n\r\n        override fun onIsPlayingChanged(isPlaying: Boolean) {\r\n            triggerIsPlayingChanged(isPlaying)\r\n        }\r\n\r\n        override fun onTracksChanged(tracks: Tracks) {\r\n            metadata = metadata.copy(\r\n                videoTracks = emptyList(),\r\n                audioTracks = emptyList(),\r\n                subtitleTracks = emptyList()\r\n            )\r\n            triggerMetadata(metadata)\r\n\r\n            val videoFormats = videoPlayer.currentTracks.groups\r\n                .filter { it.mediaTrackGroup.type == C.TRACK_TYPE_VIDEO }\r\n                .flatMap { group ->\r\n                    List(group.mediaTrackGroup.length) { trackIndex ->\r\n                        group.mediaTrackGroup\r\n                            .getFormat(trackIndex)\r\n                            .toVideoMetadata()\r\n                            .copy(isSelected = group.isTrackSelected(trackIndex))\r\n                    }\r\n                }\r\n                .mapIndexed { index, metadata ->\r\n                    metadata.copy(index = index)\r\n                }\r\n\r\n            metadata = metadata.copy(videoTracks = videoFormats)\r\n\r\n            val audioFormats = videoPlayer.currentTracks.groups\r\n                .filter { it.mediaTrackGroup.type == C.TRACK_TYPE_AUDIO }\r\n                .flatMap { group ->\r\n                    List(group.mediaTrackGroup.length) { trackIndex ->\r\n                        group.mediaTrackGroup\r\n                            .getFormat(trackIndex)\r\n                            .toAudioMetadata()\r\n                            .copy(isSelected = group.isTrackSelected(trackIndex))\r\n                    }\r\n                }\r\n                .mapIndexed { index, metadata ->\r\n                    metadata.copy(index = index)\r\n                }\r\n\r\n            metadata = metadata.copy(audioTracks = audioFormats)\r\n\r\n            val subtitleFormats = videoPlayer.currentTracks.groups\r\n                .filter { it.mediaTrackGroup.type == C.TRACK_TYPE_TEXT }\r\n                .flatMap { group ->\r\n                    List(group.mediaTrackGroup.length) { trackIndex ->\r\n                        group.mediaTrackGroup\r\n                            .getFormat(trackIndex)\r\n                            .takeIf { it.roleFlags == C.ROLE_FLAG_SUBTITLE }\r\n                            ?.toSubtitleMetadata()\r\n                            ?.copy(isSelected = group.isTrackSelected(trackIndex))\r\n                    }\r\n                }\r\n                .mapNotNull { it }\r\n                .mapIndexed { index, metadata ->\r\n                    metadata.copy(index = index)\r\n                }\r\n\r\n            metadata = metadata.copy(\r\n                subtitleTracks = subtitleFormats,\r\n                subtitle = subtitleFormats.firstOrNull { it.isSelected == true },\r\n            )\r\n\r\n            triggerMetadata(metadata)\r\n        }\r\n\r\n        override fun onCues(cueGroup: CueGroup) {\r\n            triggerCues(cueGroup.cues)\r\n        }\r\n    }\r\n\r\n    private fun Int.fromIndexFindTrack(type: @C.TrackType Int): Pair<TrackGroup, Int> {\r\n        val groups = videoPlayer.currentTracks.groups\r\n            .filter { group -> group.mediaTrackGroup.type == type }\r\n            .map { it.mediaTrackGroup }\r\n\r\n        var trackCount = 0\r\n        val group = groups.first { group ->\r\n            trackCount += group.length\r\n            this < trackCount\r\n        }\r\n\r\n        val trackIndex = this - (trackCount - group.length)\r\n\r\n        return Pair(group, trackIndex)\r\n    }\r\n\r\n    private fun Format.toVideoMetadata(video: Metadata.Video? = null): Metadata.Video {\r\n        return (video ?: Metadata.Video()).copy(\r\n            width = width,\r\n            height = height,\r\n            color = colorInfo?.toLogString(),\r\n            frameRate = frameRate,\r\n            // TODO 帧率、比特率目前是从tag中获取，有的返回空，后续需要实时计算\r\n            bitrate = bitrate,\r\n            mimeType = sampleMimeType,\r\n        )\r\n    }\r\n\r\n    private fun Format.toAudioMetadata(audio: Metadata.Audio? = null): Metadata.Audio {\r\n        return (audio ?: Metadata.Audio()).copy(\r\n            channels = channelCount,\r\n            channelsLabel = if (sampleMimeType == MimeTypes.AUDIO_AV3A) \"菁彩声\" else null,\r\n            sampleRate = sampleRate,\r\n            bitrate = bitrate,\r\n            mimeType = sampleMimeType,\r\n            language = language,\r\n        )\r\n    }\r\n\r\n    private fun Format.toSubtitleMetadata(subtitle: Metadata.Subtitle? = null): Metadata.Subtitle {\r\n        return (subtitle ?: Metadata.Subtitle()).copy(\r\n            bitrate = bitrate,\r\n            mimeType = sampleMimeType,\r\n            language = language,\r\n        )\r\n    }\r\n\r\n    private val metadataListener = object : AnalyticsListener {\r\n        override fun onVideoInputFormatChanged(\r\n            eventTime: AnalyticsListener.EventTime,\r\n            format: Format,\r\n            decoderReuseEvaluation: DecoderReuseEvaluation?,\r\n        ) {\r\n            metadata = metadata.copy(video = format.toVideoMetadata(metadata.video))\r\n            triggerMetadata(metadata)\r\n        }\r\n\r\n        override fun onVideoDecoderInitialized(\r\n            eventTime: AnalyticsListener.EventTime,\r\n            decoderName: String,\r\n            initializedTimestampMs: Long,\r\n            initializationDurationMs: Long,\r\n        ) {\r\n            metadata = metadata.copy(\r\n                video = (metadata.video ?: Metadata.Video()).copy(decoder = decoderName)\r\n            )\r\n\r\n            triggerMetadata(metadata)\r\n        }\r\n\r\n        override fun onAudioInputFormatChanged(\r\n            eventTime: AnalyticsListener.EventTime,\r\n            format: Format,\r\n            decoderReuseEvaluation: DecoderReuseEvaluation?,\r\n        ) {\r\n            metadata = metadata.copy(audio = format.toAudioMetadata(metadata.audio))\r\n            triggerMetadata(metadata)\r\n        }\r\n\r\n        override fun onAudioDecoderInitialized(\r\n            eventTime: AnalyticsListener.EventTime,\r\n            decoderName: String,\r\n            initializedTimestampMs: Long,\r\n            initializationDurationMs: Long,\r\n        ) {\r\n            metadata = metadata.copy(\r\n                audio = (metadata.audio ?: Metadata.Audio()).copy(decoder = decoderName)\r\n            )\r\n\r\n            triggerMetadata(metadata)\r\n        }\r\n    }\r\n\r\n    private val eventLogger = EventLogger()\r\n\r\n    override fun initialize() {\r\n        super.initialize()\r\n        videoPlayer.addListener(playerListener)\r\n        videoPlayer.addAnalyticsListener(metadataListener)\r\n        videoPlayer.addAnalyticsListener(eventLogger)\r\n    }\r\n\r\n    override fun release() {\r\n        onCuesListeners.clear()\r\n        videoPlayer.removeListener(playerListener)\r\n        videoPlayer.removeAnalyticsListener(metadataListener)\r\n        videoPlayer.removeAnalyticsListener(eventLogger)\r\n        videoPlayer.stop()\r\n        videoPlayer.release()\r\n        super.release()\r\n    }\r\n\r\n    override fun prepare(line: ChannelLine) {\r\n        if (Configs.videoPlayerStopPreviousMediaItem)\r\n            videoPlayer.stop()\r\n\r\n        contentTypeAttempts.clear()\r\n        currentChannelLine = line\r\n        prepare(null)\r\n    }\r\n\r\n    override fun play() {\r\n        videoPlayer.play()\r\n    }\r\n\r\n    override fun pause() {\r\n        videoPlayer.pause()\r\n    }\r\n\r\n    override fun seekTo(position: Long) {\r\n        videoPlayer.seekTo(position)\r\n    }\r\n\r\n    override fun setVolume(volume: Float) {\r\n        videoPlayer.volume = volume\r\n    }\r\n\r\n    override fun getVolume(): Float {\r\n        return videoPlayer.volume\r\n    }\r\n\r\n    override fun stop() {\r\n        videoPlayer.stop()\r\n        updatePositionJob?.cancel()\r\n        super.stop()\r\n    }\r\n\r\n    override fun selectVideoTrack(track: Metadata.Video?) {\r\n        if (track?.index == null) {\r\n            videoPlayer.trackSelectionParameters = videoPlayer.trackSelectionParameters\r\n                .buildUpon()\r\n                .setTrackTypeDisabled(C.TRACK_TYPE_VIDEO, true)\r\n                .build()\r\n\r\n            return\r\n        }\r\n\r\n        val (group, trackIndex) = track.index.fromIndexFindTrack(C.TRACK_TYPE_VIDEO)\r\n\r\n        videoPlayer.trackSelectionParameters = videoPlayer.trackSelectionParameters\r\n            .buildUpon()\r\n            .setTrackTypeDisabled(C.TRACK_TYPE_VIDEO, false)\r\n            .setOverrideForType(TrackSelectionOverride(group, trackIndex))\r\n            .build()\r\n    }\r\n\r\n    override fun selectAudioTrack(track: Metadata.Audio?) {\r\n        if (track?.index == null) {\r\n            videoPlayer.trackSelectionParameters = videoPlayer.trackSelectionParameters\r\n                .buildUpon()\r\n                .setTrackTypeDisabled(C.TRACK_TYPE_AUDIO, true)\r\n                .build()\r\n\r\n            return\r\n        }\r\n\r\n        val (group, trackIndex) = track.index.fromIndexFindTrack(C.TRACK_TYPE_AUDIO)\r\n\r\n        videoPlayer.trackSelectionParameters = videoPlayer.trackSelectionParameters\r\n            .buildUpon()\r\n            .setTrackTypeDisabled(C.TRACK_TYPE_AUDIO, false)\r\n            .setOverrideForType(TrackSelectionOverride(group, trackIndex))\r\n            .build()\r\n    }\r\n\r\n    override fun selectSubtitleTrack(track: Metadata.Subtitle?) {\r\n        if (track?.language == null) {\r\n            videoPlayer.trackSelectionParameters = videoPlayer.trackSelectionParameters\r\n                .buildUpon()\r\n                .setTrackTypeDisabled(C.TRACK_TYPE_TEXT, true)\r\n                .build()\r\n\r\n            return\r\n        }\r\n\r\n        videoPlayer.trackSelectionParameters = videoPlayer.trackSelectionParameters\r\n            .buildUpon()\r\n            .setTrackTypeDisabled(C.TRACK_TYPE_TEXT, false)\r\n            .setPreferredTextLanguages(track.language)\r\n            .build()\r\n    }\r\n\r\n    override fun setVideoSurfaceView(surfaceView: SurfaceView) {\r\n        this.surfaceView = surfaceView\r\n        videoPlayer.setVideoSurfaceView(surfaceView)\r\n    }\r\n\r\n    override fun setVideoTextureView(textureView: TextureView) {\r\n        this.textureView = textureView\r\n        videoPlayer.setVideoTextureView(textureView)\r\n    }\r\n}
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/videoplayer/player/Media3VideoPlayer.kt b/tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/videoplayer/player/Media3VideoPlayer.kt
--- a/tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/videoplayer/player/Media3VideoPlayer.kt	(revision ****************************************)
+++ b/tv/src/main/java/top/yogiczy/mytv/tv/ui/screensold/videoplayer/player/Media3VideoPlayer.kt	(date 1755499904294)
@@ -92,8 +92,6 @@
                 .build()
         }
 
-        MediaCodecVideoRenderer.skipMultipleFramesOnSameVsync =
-            Configs.videoPlayerSkipMultipleFramesOnSameVSync
         return ExoPlayer.Builder(context)
             .setRenderersFactory(renderersFactory)
             .setTrackSelector(trackSelector)
@@ -417,7 +415,7 @@
     private fun Format.toAudioMetadata(audio: Metadata.Audio? = null): Metadata.Audio {
         return (audio ?: Metadata.Audio()).copy(
             channels = channelCount,
-            channelsLabel = if (sampleMimeType == MimeTypes.AUDIO_AV3A) "菁彩声" else null,
+            channelsLabel = null,
             sampleRate = sampleRate,
             bitrate = bitrate,
             mimeType = sampleMimeType,
Index: tv/src/main/java/top/yogiczy/mytv/tv/MyTVApplication.kt
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package top.yogiczy.mytv.tv\r\n\r\nimport android.app.Application\r\nimport android.content.Intent\r\nimport coil.ImageLoader\r\nimport coil.ImageLoaderFactory\r\nimport coil.decode.SvgDecoder\r\nimport coil.disk.DiskCache\r\nimport coil.memory.MemoryCache\r\nimport coil.request.CachePolicy\r\nimport coil.util.DebugLogger\r\nimport io.sentry.Hint\r\nimport io.sentry.Sentry\r\nimport io.sentry.SentryEvent\r\nimport io.sentry.SentryLevel\r\nimport io.sentry.SentryOptions\r\nimport io.sentry.android.core.SentryAndroid\r\nimport top.yogiczy.mytv.core.data.AppData\r\nimport top.yogiczy.mytv.core.data.utils.Globals\r\nimport kotlin.system.exitProcess\r\n\r\nclass MyTVApplication : Application(), ImageLoaderFactory {\r\n    override fun onCreate() {\r\n        super.onCreate()\r\n\r\n        initSentry()\r\n        crashHandle()\r\n        AppData.init(applicationContext)\r\n        UnsafeTrustManager.enableUnsafeTrustManager()\r\n    }\r\n\r\n    override fun newImageLoader(): ImageLoader {\r\n        return ImageLoader(this).newBuilder()\r\n            .logger(DebugLogger())\r\n            .components {\r\n                add(SvgDecoder.Factory())\r\n            }\r\n            .crossfade(true)\r\n            .memoryCachePolicy(CachePolicy.ENABLED)\r\n            .memoryCache {\r\n                MemoryCache.Builder(this)\r\n                    // .maxSizePercent(0.25)\r\n                    .build()\r\n            }\r\n            .diskCachePolicy(CachePolicy.ENABLED)\r\n            .diskCache {\r\n                DiskCache.Builder()\r\n                    .directory(cacheDir.resolve(\"image_cache\"))\r\n                    // .maxSizeBytes(1024 * 1024 * 100)\r\n                    .build()\r\n            }\r\n            .build()\r\n    }\r\n\r\n    private fun initSentry() {\r\n        SentryAndroid.init(this) { options ->\r\n            options.environment = BuildConfig.BUILD_TYPE\r\n            options.dsn = BuildConfig.SENTRY_DSN\r\n            options.tracesSampleRate = 1.0\r\n            options.beforeSend =\r\n                SentryOptions.BeforeSendCallback { event: SentryEvent, _: Hint ->\r\n                    if (event.level == null) event.level = SentryLevel.FATAL\r\n\r\n                    if (BuildConfig.DEBUG) return@BeforeSendCallback null\r\n                    if (SentryLevel.ERROR != event.level && SentryLevel.FATAL != event.level) return@BeforeSendCallback null\r\n                    if (event.exceptions?.any { ex -> ex.type?.contains(\"Http\") == true } == true) return@BeforeSendCallback null\r\n\r\n                    event\r\n                }\r\n        }\r\n\r\n        @Suppress(\"UnstableApiUsage\")\r\n        Sentry.withScope { scope ->\r\n            Globals.deviceId = scope.options.distinctId ?: \"\"\r\n        }\r\n    }\r\n\r\n    private fun crashHandle() {\r\n        Thread.setDefaultUncaughtExceptionHandler { _, throwable ->\r\n            throwable.printStackTrace()\r\n            Sentry.captureException(throwable)\r\n\r\n            val intent = Intent(this, CrashHandlerActivity::class.java).apply {\r\n                putExtra(\"error_message\", throwable.message)\r\n                putExtra(\"error_stacktrace\", throwable.stackTraceToString())\r\n                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)\r\n            }\r\n            startActivity(intent)\r\n\r\n            android.os.Process.killProcess(android.os.Process.myPid())\r\n            exitProcess(1)\r\n        }\r\n    }\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/tv/src/main/java/top/yogiczy/mytv/tv/MyTVApplication.kt b/tv/src/main/java/top/yogiczy/mytv/tv/MyTVApplication.kt
--- a/tv/src/main/java/top/yogiczy/mytv/tv/MyTVApplication.kt	(revision ****************************************)
+++ b/tv/src/main/java/top/yogiczy/mytv/tv/MyTVApplication.kt	(date 1755500768509)
@@ -17,6 +17,7 @@
 import io.sentry.android.core.SentryAndroid
 import top.yogiczy.mytv.core.data.AppData
 import top.yogiczy.mytv.core.data.utils.Globals
+import top.yogiczy.mytv.tv.ui.utils.Configs
 import kotlin.system.exitProcess
 
 class MyTVApplication : Application(), ImageLoaderFactory {
@@ -27,6 +28,11 @@
         crashHandle()
         AppData.init(applicationContext)
         UnsafeTrustManager.enableUnsafeTrustManager()
+
+        // 初始化默认云同步：首次运行时，若未设置网络链接则使用编译期默认地址
+        if (Configs.cloudSyncNetworkUrl.isBlank() && BuildConfig.DEFAULT_CLOUD_SYNC_URL.isNotBlank()) {
+            Configs.cloudSyncNetworkUrl = BuildConfig.DEFAULT_CLOUD_SYNC_URL
+        }
     }
 
     override fun newImageLoader(): ImageLoader {
Index: core/data/src/main/java/top/yogiczy/mytv/core/data/repositories/iptv/parser/M3uIptvParser.kt
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package top.yogiczy.mytv.core.data.repositories.iptv.parser\r\n\r\nimport kotlinx.coroutines.Dispatchers\r\nimport kotlinx.coroutines.withContext\r\n\r\n/**\r\n * m3u直播源解析\r\n */\r\nclass M3uIptvParser : IptvParser {\r\n\r\n    override fun isSupport(url: String, data: String): Boolean {\r\n        return data.startsWith(\"#EXTM3U\")\r\n    }\r\n\r\n    override suspend fun parse(data: String) =\r\n        withContext(Dispatchers.Default) {\r\n            val lines = data.split(\"\\r\\n\", \"\\n\")\r\n            val channelList = mutableListOf<IptvParser.ChannelItem>()\r\n\r\n            var addedChannels: List<IptvParser.ChannelItem> = listOf()\r\n            lines.forEach { line ->\r\n                if (line.isBlank()) return@forEach\r\n\r\n                if (line.startsWith(\"#EXTINF\")) {\r\n                    val name = line.split(\",\").last().trim()\r\n                    val epgName =\r\n                        Regex(\"tvg-name=\\\"(.*?)\\\"\").find(line)?.groupValues?.get(1)?.trim()\r\n                            ?.ifBlank { name } ?: name\r\n                    val groupNames =\r\n                        Regex(\"group-title=\\\"(.+?)\\\"\").find(line)?.groupValues?.get(1)?.split(\";\")\r\n                            ?.map { it.trim() }\r\n                            ?: listOf(\"其他\")\r\n                    val logo = Regex(\"tvg-logo=\\\"(.+?)\\\"\").find(line)?.groupValues?.get(1)?.trim()\r\n                    val httpUserAgent =\r\n                        Regex(\"http-user-agent=\\\"(.+?)\\\"\").find(line)?.groupValues?.get(1)?.trim()\r\n\r\n                    addedChannels = groupNames.map { groupName ->\r\n                        IptvParser.ChannelItem(\r\n                            name = name,\r\n                            epgName = epgName,\r\n                            groupName = groupName,\r\n                            url = \"\",\r\n                            logo = logo,\r\n                            httpUserAgent = httpUserAgent,\r\n                        )\r\n                    }\r\n                } else if (line.startsWith(\"#KODIPROP:inputstream.adaptive.manifest_type\")) {\r\n                    addedChannels =\r\n                        addedChannels.map { it.copy(manifestType = line.split(\"=\").last()) }\r\n                } else if (line.startsWith(\"#KODIPROP:inputstream.adaptive.license_type\")) {\r\n                    addedChannels =\r\n                        addedChannels.map { it.copy(licenseType = line.split(\"=\").last()) }\r\n                } else if (line.startsWith(\"#KODIPROP:inputstream.adaptive.license_key\")) {\r\n                    addedChannels =\r\n                        addedChannels.map { it.copy(licenseKey = line.split(\"=\").last()) }\r\n                } else if (line.startsWith(\"#\") || line.startsWith(\"//\")) {\r\n                    return@forEach\r\n                } else {\r\n                    channelList.addAll(addedChannels.map { it.copy(url = line.trim()) })\r\n                }\r\n            }\r\n\r\n            channelList\r\n        }\r\n\r\n    override suspend fun getEpgUrl(data: String): String? {\r\n        val lines = data.split(\"\\r\\n\", \"\\n\")\r\n        return lines.firstOrNull { it.startsWith(\"#EXTM3U\") }?.let { defLine ->\r\n            Regex(\"x-tvg-url=\\\"(.*?)\\\"\").find(defLine)?.groupValues?.get(1)\r\n                ?.split(\",\")\r\n                ?.firstOrNull()\r\n                ?.trim()\r\n                ?: Regex(\"url-tvg=\\\"(.*?)\\\"\").find(defLine)?.groupValues?.get(1)\r\n                    ?.split(\",\")\r\n                    ?.firstOrNull()\r\n                    ?.trim()\r\n        }\r\n    }\r\n}
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/core/data/src/main/java/top/yogiczy/mytv/core/data/repositories/iptv/parser/M3uIptvParser.kt b/core/data/src/main/java/top/yogiczy/mytv/core/data/repositories/iptv/parser/M3uIptvParser.kt
--- a/core/data/src/main/java/top/yogiczy/mytv/core/data/repositories/iptv/parser/M3uIptvParser.kt	(revision ****************************************)
+++ b/core/data/src/main/java/top/yogiczy/mytv/core/data/repositories/iptv/parser/M3uIptvParser.kt	(date 1755504633092)
@@ -33,6 +33,8 @@
                     val logo = Regex("tvg-logo=\"(.+?)\"").find(line)?.groupValues?.get(1)?.trim()
                     val httpUserAgent =
                         Regex("http-user-agent=\"(.+?)\"").find(line)?.groupValues?.get(1)?.trim()
+                    val catchupSource =
+                        Regex("catchup-source=\"(.+?)\"").find(line)?.groupValues?.get(1)?.trim()
 
                     addedChannels = groupNames.map { groupName ->
                         IptvParser.ChannelItem(
@@ -42,6 +44,7 @@
                             url = "",
                             logo = logo,
                             httpUserAgent = httpUserAgent,
+                            catchupSource = catchupSource,
                         )
                     }
                 } else if (line.startsWith("#KODIPROP:inputstream.adaptive.manifest_type")) {
Index: core/data/src/main/java/top/yogiczy/mytv/core/data/repositories/iptv/parser/IptvParser.kt
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package top.yogiczy.mytv.core.data.repositories.iptv.parser\r\n\r\nimport kotlinx.serialization.Serializable\r\nimport top.yogiczy.mytv.core.data.entities.channel.Channel\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelGroup\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelGroupList\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelLine\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelLineList\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelList\r\nimport top.yogiczy.mytv.core.data.utils.ChannelAlias\r\n\r\n/**\r\n * 直播源数据解析接口\r\n */\r\ninterface IptvParser {\r\n    /**\r\n     * 是否支持该直播源格式\r\n     */\r\n    fun isSupport(url: String, data: String): Boolean\r\n\r\n    /**\r\n     * 解析直播源数据\r\n     */\r\n    suspend fun parse(data: String): List<ChannelItem>\r\n\r\n    suspend fun getEpgUrl(data: String): String? {\r\n        return null\r\n    }\r\n\r\n    companion object {\r\n        val instances = listOf(\r\n            M3uIptvParser(),\r\n            TxtIptvParser(),\r\n            DefaultIptvParser(),\r\n        )\r\n    }\r\n\r\n    @Serializable\r\n    data class ChannelItem(\r\n        val groupName: String,\r\n        val name: String,\r\n        val epgName: String = name,\r\n        val url: String,\r\n        val logo: String? = null,\r\n        val httpUserAgent: String? = null,\r\n        val manifestType: String? = null,\r\n        val licenseType: String? = null,\r\n        val licenseKey: String? = null,\r\n    ) {\r\n        companion object {\r\n            private fun List<ChannelItem>.toChannelList(): ChannelList {\r\n                return ChannelList(groupBy { it.name }\r\n                    .map { (channelName, channelList) ->\r\n                        val first = channelList.first()\r\n\r\n                        Channel(\r\n                            name = channelName,\r\n                            standardName = ChannelAlias.standardChannelName(channelName),\r\n                            epgName = ChannelAlias.standardChannelName(first.epgName),\r\n                            lineList = ChannelLineList(\r\n                                channelList.distinctBy { it.url }\r\n                                    .map {\r\n                                        ChannelLine(\r\n                                            url = it.url,\r\n                                            httpUserAgent = it.httpUserAgent,\r\n                                            manifestType = it.manifestType,\r\n                                            licenseType = it.licenseType,\r\n                                            licenseKey = it.licenseKey,\r\n                                        )\r\n                                    }\r\n                            ),\r\n                            logo = first.logo,\r\n                        )\r\n                    })\r\n            }\r\n\r\n            fun List<ChannelItem>.toChannelGroupList(): ChannelGroupList {\r\n                return ChannelGroupList(groupBy { it.groupName }\r\n                    .map { (groupName, channelList) ->\r\n                        ChannelGroup(\r\n                            name = groupName,\r\n                            channelList = channelList.toChannelList(),\r\n                        )\r\n                    })\r\n            }\r\n        }\r\n    }\r\n}
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/core/data/src/main/java/top/yogiczy/mytv/core/data/repositories/iptv/parser/IptvParser.kt b/core/data/src/main/java/top/yogiczy/mytv/core/data/repositories/iptv/parser/IptvParser.kt
--- a/core/data/src/main/java/top/yogiczy/mytv/core/data/repositories/iptv/parser/IptvParser.kt	(revision ****************************************)
+++ b/core/data/src/main/java/top/yogiczy/mytv/core/data/repositories/iptv/parser/IptvParser.kt	(date 1755504613885)
@@ -46,6 +46,7 @@
         val manifestType: String? = null,
         val licenseType: String? = null,
         val licenseKey: String? = null,
+        val catchupSource: String? = null,
     ) {
         companion object {
             private fun List<ChannelItem>.toChannelList(): ChannelList {
@@ -66,6 +67,7 @@
                                             manifestType = it.manifestType,
                                             licenseType = it.licenseType,
                                             licenseKey = it.licenseKey,
+                                            catchupSource = it.catchupSource,
                                         )
                                     }
                             ),
Index: allinone/.gitignore
===================================================================
diff --git a/allinone/.gitignore b/allinone/.gitignore
deleted file mode 100644
--- a/allinone/.gitignore	(revision ****************************************)
+++ /dev/null	(revision ****************************************)
@@ -1,1 +0,0 @@
-/build
\ No newline at end of file
Index: allinone/src/main/java/top/yogiczy/mytv/allinone/AllInOne.kt
===================================================================
diff --git a/allinone/src/main/java/top/yogiczy/mytv/allinone/AllInOne.kt b/allinone/src/main/java/top/yogiczy/mytv/allinone/AllInOne.kt
deleted file mode 100644
--- a/allinone/src/main/java/top/yogiczy/mytv/allinone/AllInOne.kt	(revision ****************************************)
+++ /dev/null	(revision ****************************************)
@@ -1,104 +0,0 @@
-package top.yogiczy.mytv.allinone
-
-import android.content.Context
-import android.util.Log
-import kotlinx.coroutines.Dispatchers
-import kotlinx.coroutines.withContext
-import java.io.BufferedReader
-import java.io.File
-import java.io.InputStreamReader
-
-object AllInOne {
-    private const val TAG = "AllInOne"
-    private var process: Process? = null
-
-    suspend fun start(
-        context: Context,
-        allinonePath: String,
-        onFail: () -> Unit = {},
-        onUnsupported: () -> Unit = {},
-    ) {
-        if (process != null) return
-
-        withContext(Dispatchers.IO) {
-            runCatching {
-                val proot = File(context.applicationInfo.nativeLibraryDir, "libproot.so")
-                val loader = File(context.applicationInfo.nativeLibraryDir, "libloader.so")
-                val libTermuxExec =
-                    File(context.applicationInfo.nativeLibraryDir, "libtermux-exec.so")
-
-                if (!proot.exists()) {
-                    onUnsupported()
-                    return@runCatching
-                }
-
-                val allinoneBin = File(allinonePath.split(" ").first())
-                val allinoneParams = allinonePath.split(" ").drop(1).joinToString(" ")
-
-                val allinone = File(context.filesDir, "allinone").apply {
-                    if (allinoneBin.lastModified() > lastModified()) {
-                        allinoneBin.copyTo(this, true)
-                        setExecutable(true)
-                    }
-                }
-
-                val prootDir = File(context.filesDir, "proot").apply { mkdirs() }
-                val rootfsDir = File(prootDir, "rootfs").apply { mkdirs() }
-                val pwdDir = File(prootDir, "pwd").apply { mkdirs() }
-                val tmpDir = File(prootDir, "tmp").apply { mkdirs() }
-
-                val etcResolvConf = File(rootfsDir, "etc/resolv.conf").apply {
-                    parentFile?.mkdirs()
-                    context.assets.open("resolv.conf").copyTo(outputStream())
-                }
-                val etcSsl = File(rootfsDir, "etc/ssl").apply {
-                    mkdirs()
-                    File(this, "certs/ca-bundle.crt").apply {
-                        parentFile?.mkdirs()
-                        context.assets.open("ca-bundle.crt").copyTo(outputStream())
-                    }
-                }
-
-                val processBuilder = ProcessBuilder(
-                    "sh", "-c",
-                    listOf(
-                        "export PROOT_LOADER=${loader.absolutePath}",
-                        "&& export PROOT_TMP_DIR=${tmpDir.absolutePath}",
-                        "&& export LD_PRELOAD=${libTermuxExec.absolutePath}",
-                        "&& ${proot.absolutePath}",
-                        "-b ${etcResolvConf.absolutePath}:/etc/resolv.conf",
-                        "-b ${etcSsl}:/etc/ssl",
-                        "-w ${pwdDir.absolutePath}",
-                        " ${allinone.absolutePath}",
-                        " $allinoneParams",
-                    ).joinToString(" ")
-                )
-
-                processBuilder.redirectErrorStream(true)
-                process = processBuilder.start().apply {
-                    val reader = BufferedReader(InputStreamReader(inputStream))
-
-                    while (true) {
-                        val line = reader.readLine() ?: break
-                        Log.d(TAG, line)
-                    }
-                }
-
-                val ret = process?.waitFor()
-                stop()
-
-                if (ret != 0) onFail()
-            }.onFailure {
-                onFail()
-                it.printStackTrace()
-            }
-        }
-    }
-
-    suspend fun stop() {
-        withContext(Dispatchers.IO) {
-            process?.destroy()
-            process = null
-        }
-    }
-}
\ No newline at end of file
Index: allinone/src/main/AndroidManifest.xml
===================================================================
diff --git a/allinone/src/main/AndroidManifest.xml b/allinone/src/main/AndroidManifest.xml
deleted file mode 100644
--- a/allinone/src/main/AndroidManifest.xml	(revision ****************************************)
+++ /dev/null	(revision ****************************************)
@@ -1,4 +0,0 @@
-<?xml version="1.0" encoding="utf-8"?>
-<manifest xmlns:android="http://schemas.android.com/apk/res/android">
-
-</manifest>
\ No newline at end of file
Index: allinone/build.gradle.kts
===================================================================
diff --git a/allinone/build.gradle.kts b/allinone/build.gradle.kts
deleted file mode 100644
--- a/allinone/build.gradle.kts	(revision ****************************************)
+++ /dev/null	(revision ****************************************)
@@ -1,47 +0,0 @@
-plugins {
-    alias(libs.plugins.android.library)
-    alias(libs.plugins.kotlin.android)
-}
-
-android {
-    namespace = "top.yogiczy.mytv.allinone"
-    compileSdk = libs.versions.compileSdk.get().toInt()
-
-    defaultConfig {
-        minSdk = libs.versions.minSdk.get().toInt()
-
-        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
-        consumerProguardFiles("consumer-rules.pro")
-    }
-
-    buildTypes {
-        release {
-            isMinifyEnabled = false
-            proguardFiles(
-                getDefaultProguardFile("proguard-android-optimize.txt"),
-                "proguard-rules.pro"
-            )
-        }
-    }
-    compileOptions {
-        sourceCompatibility = JavaVersion.VERSION_11
-        targetCompatibility = JavaVersion.VERSION_11
-    }
-    kotlinOptions {
-        jvmTarget = "11"
-    }
-    sourceSets {
-        getByName("main") {
-            jniLibs.srcDirs("jniLibs")
-        }
-    }
-}
-
-dependencies {
-
-    implementation(libs.androidx.core.ktx)
-    implementation(libs.androidx.appcompat)
-    testImplementation(libs.junit)
-    androidTestImplementation(libs.androidx.junit)
-    androidTestImplementation(libs.androidx.espresso.core)
-}
\ No newline at end of file
Index: allinone/src/androidTest/java/top/yogiczy/mytv/allinone/ExampleInstrumentedTest.kt
===================================================================
diff --git a/allinone/src/androidTest/java/top/yogiczy/mytv/allinone/ExampleInstrumentedTest.kt b/allinone/src/androidTest/java/top/yogiczy/mytv/allinone/ExampleInstrumentedTest.kt
deleted file mode 100644
--- a/allinone/src/androidTest/java/top/yogiczy/mytv/allinone/ExampleInstrumentedTest.kt	(revision ****************************************)
+++ /dev/null	(revision ****************************************)
@@ -1,24 +0,0 @@
-package top.yogiczy.mytv.allinone
-
-import androidx.test.platform.app.InstrumentationRegistry
-import androidx.test.ext.junit.runners.AndroidJUnit4
-
-import org.junit.Test
-import org.junit.runner.RunWith
-
-import org.junit.Assert.*
-
-/**
- * Instrumented test, which will execute on an Android device.
- *
- * See [testing documentation](http://d.android.com/tools/testing).
- */
-@RunWith(AndroidJUnit4::class)
-class ExampleInstrumentedTest {
-    @Test
-    fun useAppContext() {
-        // Context of the app under test.
-        val appContext = InstrumentationRegistry.getInstrumentation().targetContext
-        assertEquals("top.yogiczy.mytv.allinone.test", appContext.packageName)
-    }
-}
\ No newline at end of file
Index: allinone/src/test/java/top/yogiczy/mytv/allinone/ExampleUnitTest.kt
===================================================================
diff --git a/allinone/src/test/java/top/yogiczy/mytv/allinone/ExampleUnitTest.kt b/allinone/src/test/java/top/yogiczy/mytv/allinone/ExampleUnitTest.kt
deleted file mode 100644
--- a/allinone/src/test/java/top/yogiczy/mytv/allinone/ExampleUnitTest.kt	(revision ****************************************)
+++ /dev/null	(revision ****************************************)
@@ -1,17 +0,0 @@
-package top.yogiczy.mytv.allinone
-
-import org.junit.Test
-
-import org.junit.Assert.*
-
-/**
- * Example local unit test, which will execute on the development machine (host).
- *
- * See [testing documentation](http://d.android.com/tools/testing).
- */
-class ExampleUnitTest {
-    @Test
-    fun addition_isCorrect() {
-        assertEquals(4, 2 + 2)
-    }
-}
\ No newline at end of file
Index: allinone/proguard-rules.pro
===================================================================
diff --git a/allinone/proguard-rules.pro b/allinone/proguard-rules.pro
deleted file mode 100644
--- a/allinone/proguard-rules.pro	(revision ****************************************)
+++ /dev/null	(revision ****************************************)
@@ -1,21 +0,0 @@
-# Add project specific ProGuard rules here.
-# You can control the set of applied configuration files using the
-# proguardFiles setting in build.gradle.
-#
-# For more details, see
-#   http://developer.android.com/guide/developing/tools/proguard.html
-
-# If your project uses WebView with JS, uncomment the following
-# and specify the fully qualified class name to the JavaScript interface
-# class:
-#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
-#   public *;
-#}
-
-# Uncomment this to preserve the line number information for
-# debugging stack traces.
-#-keepattributes SourceFile,LineNumberTable
-
-# If you keep the line number information, uncomment this to
-# hide the original source file name.
-#-renamesourcefileattribute SourceFile
\ No newline at end of file
Index: core/data/src/main/java/top/yogiczy/mytv/core/data/entities/channel/ChannelLine.kt
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package top.yogiczy.mytv.core.data.entities.channel\r\n\r\nimport kotlinx.serialization.Serializable\r\n\r\n/**\r\n * 频道线路\r\n */\r\n@Serializable\r\ndata class ChannelLine(\r\n    val url: String = \"\",\r\n    val httpUserAgent: String? = null,\r\n    val name: String? = if (url.contains(\"$\")) url.split(\"$\").lastOrNull() else null,\r\n    val manifestType: String? = null,\r\n    val licenseType: String? = null,\r\n    val licenseKey: String? = null,\r\n) {\r\n\r\n    val playableUrl: String\r\n        get() = url.substringBefore(\"$\").let {\r\n            // 修复部分链接无法播放，实际请求时?将去掉，导致链接无法访问，因此加上一个t\r\n            if (url.endsWith(\"?\")) \"${it}t\" else it\r\n        }\r\n\r\n    companion object {\r\n        val EXAMPLE =\r\n            ChannelLine(\r\n                url = \"http://*******\\$LR•IPV6『线路1』\",\r\n                httpUserAgent = \"okhttp\",\r\n            )\r\n    }\r\n}
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/core/data/src/main/java/top/yogiczy/mytv/core/data/entities/channel/ChannelLine.kt b/core/data/src/main/java/top/yogiczy/mytv/core/data/entities/channel/ChannelLine.kt
--- a/core/data/src/main/java/top/yogiczy/mytv/core/data/entities/channel/ChannelLine.kt	(revision ****************************************)
+++ b/core/data/src/main/java/top/yogiczy/mytv/core/data/entities/channel/ChannelLine.kt	(date 1755504587242)
@@ -13,6 +13,7 @@
     val manifestType: String? = null,
     val licenseType: String? = null,
     val licenseKey: String? = null,
+    val catchupSource: String? = null,
 ) {
 
     val playableUrl: String
Index: .idea/misc.xml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+><?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n<project version=\"4\">\r\n  <component name=\"EntryPointsManager\">\r\n    <list size=\"1\">\r\n      <item index=\"0\" class=\"java.lang.String\" itemvalue=\"android.webkit.JavascriptInterface\" />\r\n    </list>\r\n  </component>\r\n  <component name=\"ExternalStorageConfigurationManager\" enabled=\"true\" />\r\n  <component name=\"ProjectRootManager\" version=\"2\" languageLevel=\"JDK_21\" default=\"true\" project-jdk-name=\"jbr-21\" project-jdk-type=\"JavaSDK\">\r\n    <output url=\"file://$PROJECT_DIR$/build/classes\" />\r\n  </component>\r\n  <component name=\"ProjectType\">\r\n    <option name=\"id\" value=\"Android\" />\r\n  </component>\r\n</project>
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/.idea/misc.xml b/.idea/misc.xml
--- a/.idea/misc.xml	(revision ****************************************)
+++ b/.idea/misc.xml	(date 1755498420167)
@@ -1,4 +1,3 @@
-<?xml version="1.0" encoding="UTF-8"?>
 <project version="4">
   <component name="EntryPointsManager">
     <list size="1">
Index: tv/src/main/java/top/yogiczy/mytv/tv/ui/App.kt
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package top.yogiczy.mytv.tv.ui\r\n\r\nimport androidx.annotation.IntRange\r\nimport androidx.compose.foundation.layout.PaddingValues\r\nimport androidx.compose.foundation.layout.calculateEndPadding\r\nimport androidx.compose.foundation.layout.calculateStartPadding\r\nimport androidx.compose.runtime.Composable\r\nimport androidx.compose.runtime.CompositionLocalProvider\r\nimport androidx.compose.runtime.LaunchedEffect\r\nimport androidx.compose.runtime.getValue\r\nimport androidx.compose.runtime.mutableStateOf\r\nimport androidx.compose.runtime.remember\r\nimport androidx.compose.runtime.setValue\r\nimport androidx.compose.ui.Modifier\r\nimport androidx.compose.ui.platform.LocalConfiguration\r\nimport androidx.compose.ui.platform.LocalContext\r\nimport androidx.compose.ui.platform.LocalDensity\r\nimport androidx.compose.ui.platform.LocalLayoutDirection\r\nimport androidx.compose.ui.unit.Density\r\nimport androidx.compose.ui.unit.LayoutDirection\r\nimport androidx.compose.ui.unit.dp\r\nimport kotlinx.coroutines.FlowPreview\r\nimport kotlinx.coroutines.channels.Channel\r\nimport kotlinx.coroutines.flow.consumeAsFlow\r\nimport kotlinx.coroutines.flow.debounce\r\nimport top.yogiczy.mytv.allinone.AllInOne\r\nimport top.yogiczy.mytv.core.data.entities.iptvsource.IptvSource.Companion.needExternalStoragePermission\r\nimport top.yogiczy.mytv.tv.ui.material.Padding\r\nimport top.yogiczy.mytv.tv.ui.material.PopupHandleableApplication\r\nimport top.yogiczy.mytv.tv.ui.material.Snackbar\r\nimport top.yogiczy.mytv.tv.ui.material.SnackbarType\r\nimport top.yogiczy.mytv.tv.ui.material.SnackbarUI\r\nimport top.yogiczy.mytv.tv.ui.material.Visibility\r\nimport top.yogiczy.mytv.tv.ui.screen.main.MainScreen\r\nimport top.yogiczy.mytv.tv.ui.screen.monitor.MonitorPopup\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.SettingsViewModel\r\nimport top.yogiczy.mytv.tv.ui.screen.settings.settingsVM\r\nimport top.yogiczy.mytv.tv.ui.theme.DESIGN_WIDTH\r\nimport top.yogiczy.mytv.tv.ui.theme.SAFE_AREA_HORIZONTAL_PADDING\r\nimport top.yogiczy.mytv.tv.ui.theme.SAFE_AREA_VERTICAL_PADDING\r\nimport top.yogiczy.mytv.tv.ui.tooling.PreviewWithLayoutGrids\r\nimport top.yogiczy.mytv.tv.ui.utils.rememberReadExternalStoragePermission\r\nimport java.io.File\r\n\r\n@Composable\r\nfun App(\r\n    modifier: Modifier = Modifier,\r\n    settingsViewModel: SettingsViewModel = settingsVM,\r\n    onBackPressed: () -> Unit = {},\r\n) {\r\n    val context = LocalContext.current\r\n    val configuration = LocalConfiguration.current\r\n    val doubleBackPressedExitState = rememberDoubleBackPressedExitState()\r\n\r\n    CompositionLocalProvider(\r\n        LocalDensity provides Density(\r\n            density = LocalDensity.current.density * when (settingsViewModel.uiDensityScaleRatio) {\r\n                0f -> configuration.screenWidthDp.toFloat() / DESIGN_WIDTH\r\n                else -> settingsViewModel.uiDensityScaleRatio\r\n            },\r\n            fontScale = LocalDensity.current.fontScale * settingsViewModel.uiFontScaleRatio,\r\n        ),\r\n    ) {\r\n        PopupHandleableApplication {\r\n            MainScreen(\r\n                modifier = modifier,\r\n                onBackPressed = {\r\n                    if (doubleBackPressedExitState.allowExit) {\r\n                        onBackPressed()\r\n                    } else {\r\n                        doubleBackPressedExitState.backPress()\r\n                        Snackbar.show(\"再按一次退出\")\r\n                    }\r\n                },\r\n            )\r\n        }\r\n\r\n        SnackbarUI()\r\n        Visibility({ settingsViewModel.debugShowFps }) { MonitorPopup() }\r\n        Visibility({ settingsViewModel.debugShowLayoutGrids }) { PreviewWithLayoutGrids { } }\r\n    }\r\n\r\n    if (settingsViewModel.iptvSourceCurrent.needExternalStoragePermission()) {\r\n        val (hasPermission, requestPermission) = rememberReadExternalStoragePermission()\r\n        LaunchedEffect(Unit) { if (!hasPermission) requestPermission() }\r\n    }\r\n\r\n    LaunchedEffect(settingsViewModel.iptvSourceCurrent) {\r\n        if (settingsViewModel.feiyangAllInOneFilePath.isNotBlank()) {\r\n            AllInOne.start(\r\n                context,\r\n                settingsViewModel.feiyangAllInOneFilePath,\r\n                onFail = {\r\n                    Snackbar.show(\"二进制 启动失败\", type = SnackbarType.ERROR)\r\n                },\r\n                onUnsupported = {\r\n                    Snackbar.show(\"二进制 不支持当前平台\", type = SnackbarType.ERROR)\r\n                },\r\n            )\r\n        }\r\n    }\r\n}\r\n\r\n/**\r\n * 退出应用二次确认\r\n */\r\nclass DoubleBackPressedExitState internal constructor(\r\n    @IntRange(from = 0)\r\n    private val resetSeconds: Int,\r\n) {\r\n    private var _allowExit by mutableStateOf(false)\r\n    val allowExit get() = _allowExit\r\n\r\n    fun backPress() {\r\n        _allowExit = true\r\n        channel.trySend(resetSeconds)\r\n    }\r\n\r\n    private val channel = Channel<Int>(Channel.CONFLATED)\r\n\r\n    @OptIn(FlowPreview::class)\r\n    suspend fun observe() {\r\n        channel.consumeAsFlow()\r\n            .debounce { it.toLong() * 1000 }\r\n            .collect { _allowExit = false }\r\n    }\r\n}\r\n\r\n/**\r\n * 退出应用二次确认状态\r\n */\r\n@Composable\r\nfun rememberDoubleBackPressedExitState(@IntRange(from = 0) resetSeconds: Int = 2) =\r\n    remember { DoubleBackPressedExitState(resetSeconds = resetSeconds) }\r\n        .also { LaunchedEffect(it) { it.observe() } }\r\n\r\nval ParentPadding = PaddingValues(\r\n    vertical = SAFE_AREA_VERTICAL_PADDING.dp,\r\n    horizontal = SAFE_AREA_HORIZONTAL_PADDING.dp,\r\n)\r\n\r\n@Composable\r\nfun rememberChildPadding(direction: LayoutDirection = LocalLayoutDirection.current) =\r\n    remember {\r\n        Padding(\r\n            start = ParentPadding.calculateStartPadding(direction),\r\n            top = ParentPadding.calculateTopPadding(),\r\n            end = ParentPadding.calculateEndPadding(direction),\r\n            bottom = ParentPadding.calculateBottomPadding()\r\n        )\r\n    }\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/tv/src/main/java/top/yogiczy/mytv/tv/ui/App.kt b/tv/src/main/java/top/yogiczy/mytv/tv/ui/App.kt
--- a/tv/src/main/java/top/yogiczy/mytv/tv/ui/App.kt	(revision ****************************************)
+++ b/tv/src/main/java/top/yogiczy/mytv/tv/ui/App.kt	(date 1755499320832)
@@ -23,7 +23,6 @@
 import kotlinx.coroutines.channels.Channel
 import kotlinx.coroutines.flow.consumeAsFlow
 import kotlinx.coroutines.flow.debounce
-import top.yogiczy.mytv.allinone.AllInOne
 import top.yogiczy.mytv.core.data.entities.iptvsource.IptvSource.Companion.needExternalStoragePermission
 import top.yogiczy.mytv.tv.ui.material.Padding
 import top.yogiczy.mytv.tv.ui.material.PopupHandleableApplication
@@ -86,18 +85,6 @@
     }
 
     LaunchedEffect(settingsViewModel.iptvSourceCurrent) {
-        if (settingsViewModel.feiyangAllInOneFilePath.isNotBlank()) {
-            AllInOne.start(
-                context,
-                settingsViewModel.feiyangAllInOneFilePath,
-                onFail = {
-                    Snackbar.show("二进制 启动失败", type = SnackbarType.ERROR)
-                },
-                onUnsupported = {
-                    Snackbar.show("二进制 不支持当前平台", type = SnackbarType.ERROR)
-                },
-            )
-        }
     }
 }
 
Index: settings.gradle.kts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>pluginManagement {\r\n    repositories {\r\n        google {\r\n            content {\r\n                includeGroupByRegex(\"com\\\\.android.*\")\r\n                includeGroupByRegex(\"com\\\\.google.*\")\r\n                includeGroupByRegex(\"androidx.*\")\r\n            }\r\n        }\r\n        mavenCentral()\r\n        gradlePluginPortal()\r\n    }\r\n}\r\ndependencyResolutionManagement {\r\n    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)\r\n    repositories {\r\n        google()\r\n        mavenCentral()\r\n        maven { url = uri(\"https://jitpack.io\") }\r\n        maven { url = uri(\"https://maven.aliyun.com/repository/public\") }\r\n    }\r\n}\r\n\r\nrootProject.name = \"天光云影v3.3\"\r\n\r\ninclude(\":core:data\")\r\ninclude(\":core:util\")\r\ninclude(\":core:designsystem\")\r\ninclude(\":tv\")\r\ninclude(\":mobile\")\r\ninclude(\":ijkplayer-java\")\r\ninclude(\":allinone\")\r\n\r\nval mediaSettingsFile = file(\"../media/core_settings.gradle\")\r\nif (mediaSettingsFile.exists()) {\r\n    (gradle as ExtensionAware).extra[\"androidxMediaModulePrefix\"] = \"media3:\"\r\n    apply(from = mediaSettingsFile)\r\n}\r\n\r\ninclude(\":tbsx5\")\r\nproject(\":tbsx5\").projectDir = file(\"../YYKM/tbsx5\")
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/settings.gradle.kts b/settings.gradle.kts
--- a/settings.gradle.kts	(revision ****************************************)
+++ b/settings.gradle.kts	(date 1755498870299)
@@ -29,7 +29,6 @@
 include(":tv")
 include(":mobile")
 include(":ijkplayer-java")
-include(":allinone")
 
 val mediaSettingsFile = file("../media/core_settings.gradle")
 if (mediaSettingsFile.exists()) {
Index: tv/src/main/java/top/yogiczy/mytv/tv/sync/CloudSync.kt
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package top.yogiczy.mytv.tv.sync\r\n\r\nimport kotlinx.coroutines.Dispatchers\r\nimport kotlinx.coroutines.withContext\r\nimport kotlinx.serialization.Serializable\r\nimport top.yogiczy.mytv.core.data.utils.ChannelAlias\r\nimport top.yogiczy.mytv.core.data.utils.Globals\r\nimport top.yogiczy.mytv.core.data.utils.Loggable\r\nimport top.yogiczy.mytv.tv.BuildConfig\r\nimport top.yogiczy.mytv.tv.sync.repositories.CloudSyncRepository\r\nimport top.yogiczy.mytv.tv.sync.repositories.GiteeGistSyncRepository\r\nimport top.yogiczy.mytv.tv.sync.repositories.GithubGistSyncRepository\r\nimport top.yogiczy.mytv.tv.sync.repositories.LocalFileSyncRepository\r\nimport top.yogiczy.mytv.tv.sync.repositories.NetworkUrlSyncRepository\r\nimport top.yogiczy.mytv.tv.sync.repositories.WebDavSyncRepository\r\nimport top.yogiczy.mytv.tv.ui.utils.Configs\r\nimport java.io.File\r\n\r\nobject CloudSync : Loggable(\"CloudSync\") {\r\n    private fun getRepository(): CloudSyncRepository {\r\n        return when (Configs.cloudSyncProvider) {\r\n            CloudSyncProvider.GITHUB_GIST -> GithubGistSyncRepository(\r\n                Configs.cloudSyncGithubGistId,\r\n                Configs.cloudSyncGithubGistToken,\r\n            )\r\n\r\n            CloudSyncProvider.GITEE_GIST -> GiteeGistSyncRepository(\r\n                Configs.cloudSyncGiteeGistId,\r\n                Configs.cloudSyncGiteeGistToken,\r\n            )\r\n\r\n            CloudSyncProvider.NETWORK_URL -> NetworkUrlSyncRepository(Configs.cloudSyncNetworkUrl)\r\n            CloudSyncProvider.LOCAL_FILE -> LocalFileSyncRepository(Configs.cloudSyncLocalFilePath)\r\n            CloudSyncProvider.WEBDAV -> WebDavSyncRepository(\r\n                Configs.cloudSyncWebDavUrl,\r\n                Configs.cloudSyncWebDavUsername,\r\n                Configs.cloudSyncWebDavPassword,\r\n            )\r\n        }\r\n    }\r\n\r\n    suspend fun getData(): CloudSyncData = withContext(Dispatchers.IO) {\r\n        val configs = Configs.toPartial()\r\n        CloudSyncData(\r\n            version = BuildConfig.VERSION_NAME,\r\n            syncAt = System.currentTimeMillis(),\r\n            syncFrom = Globals.deviceName,\r\n            configs = configs.desensitized(),\r\n            extraLocalIptvSourceList = configs.iptvSourceList\r\n                ?.filter { it.isLocal && it.url.startsWith(Globals.fileDir.path) }\r\n                ?.associate { it.url to runCatching { File(it.url).readText() }.getOrDefault(\"\") },\r\n            extraChannelNameAlias = runCatching { ChannelAlias.aliasFile.readText() }.getOrDefault(\"\"),\r\n        )\r\n    }\r\n\r\n    suspend fun push(): Boolean {\r\n        log.i(\"推送云端数据(${Configs.cloudSyncProvider.label})\")\r\n        return getRepository().push(getData())\r\n    }\r\n\r\n    suspend fun pull(): CloudSyncData {\r\n        log.i(\"拉取云端数据(${Configs.cloudSyncProvider.label})\")\r\n        return getRepository().pull().let {\r\n            it.copy(configs = it.configs.desensitized())\r\n        }\r\n    }\r\n}\r\n\r\n@Serializable\r\ndata class CloudSyncData(\r\n    val version: String = \"\",\r\n    val syncAt: Long = 0,\r\n    val syncFrom: String = \"\",\r\n    val description: String? = null,\r\n    val configs: Configs.Partial = Configs.Partial(),\r\n    val extraLocalIptvSourceList: Map<String, String>? = null,\r\n    val extraChannelNameAlias: String? = null,\r\n) {\r\n    companion object {\r\n        val EMPTY = CloudSyncData()\r\n    }\r\n\r\n    suspend fun apply() {\r\n        val that = this\r\n        withContext(Dispatchers.IO) {\r\n            Configs.fromPartial(that.configs)\r\n            that.extraLocalIptvSourceList?.entries\r\n                ?.filter { it.key.startsWith(Globals.fileDir.path) }\r\n                ?.forEach { entry ->\r\n                    File(entry.key).writeText(entry.value)\r\n                }\r\n            that.extraChannelNameAlias?.let {\r\n                ChannelAlias.aliasFile.writeText(it)\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\nenum class CloudSyncProvider(\r\n    val value: Int,\r\n    val label: String,\r\n    val supportPull: Boolean,\r\n    val supportPush: Boolean,\r\n) {\r\n    GITHUB_GIST(0, \"GitHub Gist\", true, true),\r\n    GITEE_GIST(1, \"Gitee 代码片段\", true, true),\r\n    NETWORK_URL(2, \"网络链接\", true, false),\r\n    LOCAL_FILE(3, \"本地文件\", true, true),\r\n    WEBDAV(4, \"WebDAV\", true, true);\r\n\r\n    companion object {\r\n        fun fromValue(value: Int): CloudSyncProvider {\r\n            return CloudSyncProvider.entries.firstOrNull { it.value == value } ?: GITHUB_GIST\r\n        }\r\n    }\r\n}
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/tv/src/main/java/top/yogiczy/mytv/tv/sync/CloudSync.kt b/tv/src/main/java/top/yogiczy/mytv/tv/sync/CloudSync.kt
--- a/tv/src/main/java/top/yogiczy/mytv/tv/sync/CloudSync.kt	(revision ****************************************)
+++ b/tv/src/main/java/top/yogiczy/mytv/tv/sync/CloudSync.kt	(date 1755500641841)
@@ -60,9 +60,14 @@
 
     suspend fun pull(): CloudSyncData {
         log.i("拉取云端数据(${Configs.cloudSyncProvider.label})")
-        return getRepository().pull().let {
-            it.copy(configs = it.configs.desensitized())
+        // 特判当提供商为网络链接但未配置地址时，使用编译期默认值；若仍为空则返回空数据
+        if (Configs.cloudSyncProvider == CloudSyncProvider.NETWORK_URL) {
+            val effectiveUrl = Configs.cloudSyncNetworkUrl.ifBlank { BuildConfig.DEFAULT_CLOUD_SYNC_URL }
+            if (effectiveUrl.isBlank()) return CloudSyncData.EMPTY
+            return NetworkUrlSyncRepository(effectiveUrl).pull().let { it.copy(configs = it.configs.desensitized()) }
         }
+
+        return getRepository().pull().let { it.copy(configs = it.configs.desensitized()) }
     }
 }
 
Index: .idea/deviceManager.xml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+><?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n<project version=\"4\">\r\n  <component name=\"DeviceTable\">\r\n    <option name=\"groupByAttributes\">\r\n      <list>\r\n        <option value=\"FormFactor\" />\r\n      </list>\r\n    </option>\r\n  </component>\r\n</project>
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/.idea/deviceManager.xml b/.idea/deviceManager.xml
--- a/.idea/deviceManager.xml	(revision ****************************************)
+++ b/.idea/deviceManager.xml	(date 1755499424366)
@@ -1,6 +1,14 @@
 <?xml version="1.0" encoding="UTF-8"?>
 <project version="4">
   <component name="DeviceTable">
+    <option name="columnSorters">
+      <list>
+        <ColumnSorterState>
+          <option name="column" value="Name" />
+          <option name="order" value="ASCENDING" />
+        </ColumnSorterState>
+      </list>
+    </option>
     <option name="groupByAttributes">
       <list>
         <option value="FormFactor" />
Index: tv/src/main/java/top/yogiczy/mytv/tv/ui/screen/settings/SettingsViewModel.kt
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package top.yogiczy.mytv.tv.ui.screen.settings\r\n\r\nimport androidx.compose.runtime.Composable\r\nimport androidx.compose.runtime.getValue\r\nimport androidx.compose.runtime.mutableFloatStateOf\r\nimport androidx.compose.runtime.mutableIntStateOf\r\nimport androidx.compose.runtime.mutableLongStateOf\r\nimport androidx.compose.runtime.mutableStateOf\r\nimport androidx.compose.runtime.setValue\r\nimport androidx.lifecycle.ViewModel\r\nimport androidx.lifecycle.viewmodel.compose.viewModel\r\nimport top.yogiczy.mytv.core.data.entities.channel.Channel\r\nimport top.yogiczy.mytv.core.data.entities.channel.ChannelFavoriteList\r\nimport top.yogiczy.mytv.core.data.entities.epg.EpgProgrammeReserveList\r\nimport top.yogiczy.mytv.core.data.entities.epgsource.EpgSource\r\nimport top.yogiczy.mytv.core.data.entities.epgsource.EpgSourceList\r\nimport top.yogiczy.mytv.core.data.entities.iptvsource.IptvSource\r\nimport top.yogiczy.mytv.core.data.entities.iptvsource.IptvSourceList\r\nimport top.yogiczy.mytv.tv.sync.CloudSyncProvider\r\nimport top.yogiczy.mytv.tv.ui.screen.Screens\r\nimport top.yogiczy.mytv.tv.ui.screen.components.AppThemeDef\r\nimport top.yogiczy.mytv.tv.ui.screensold.videoplayer.VideoPlayerDisplayMode\r\nimport top.yogiczy.mytv.tv.ui.utils.Configs\r\n\r\nclass SettingsViewModel : ViewModel() {\r\n    private var _appBootLaunch by mutableStateOf(false)\r\n    var appBootLaunch: Boolean\r\n        get() = _appBootLaunch\r\n        set(value) {\r\n            _appBootLaunch = value\r\n            Configs.appBootLaunch = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _appPipEnable by mutableStateOf(false)\r\n    var appPipEnable: Boolean\r\n        get() = _appPipEnable\r\n        set(value) {\r\n            _appPipEnable = value\r\n            Configs.appPipEnable = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _appLastLatestVersion by mutableStateOf(\"\")\r\n    var appLastLatestVersion: String\r\n        get() = _appLastLatestVersion\r\n        set(value) {\r\n            _appLastLatestVersion = value\r\n            Configs.appLastLatestVersion = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _appAgreementAgreed by mutableStateOf(false)\r\n    var appAgreementAgreed: Boolean\r\n        get() = _appAgreementAgreed\r\n        set(value) {\r\n            _appAgreementAgreed = value\r\n            Configs.appAgreementAgreed = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _appStartupScreen by mutableStateOf(Screens.Dashboard.name)\r\n    var appStartupScreen: String\r\n        get() = _appStartupScreen\r\n        set(value) {\r\n            _appStartupScreen = value\r\n            Configs.appStartupScreen = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _debugDeveloperMode by mutableStateOf(false)\r\n    var debugDeveloperMode: Boolean\r\n        get() = _debugDeveloperMode\r\n        set(value) {\r\n            _debugDeveloperMode = value\r\n            Configs.debugDeveloperMode = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _debugShowFps by mutableStateOf(false)\r\n    var debugShowFps: Boolean\r\n        get() = _debugShowFps\r\n        set(value) {\r\n            _debugShowFps = value\r\n            Configs.debugShowFps = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _debugShowVideoPlayerMetadata by mutableStateOf(false)\r\n    var debugShowVideoPlayerMetadata: Boolean\r\n        get() = _debugShowVideoPlayerMetadata\r\n        set(value) {\r\n            _debugShowVideoPlayerMetadata = value\r\n            Configs.debugShowVideoPlayerMetadata = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _debugShowLayoutGrids by mutableStateOf(false)\r\n    var debugShowLayoutGrids: Boolean\r\n        get() = _debugShowLayoutGrids\r\n        set(value) {\r\n            _debugShowLayoutGrids = value\r\n            Configs.debugShowLayoutGrids = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _iptvSourceCacheTime by mutableLongStateOf(0)\r\n    var iptvSourceCacheTime: Long\r\n        get() = _iptvSourceCacheTime\r\n        set(value) {\r\n            _iptvSourceCacheTime = value\r\n            Configs.iptvSourceCacheTime = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _iptvSourceCurrent by mutableStateOf(IptvSource())\r\n    var iptvSourceCurrent: IptvSource\r\n        get() = _iptvSourceCurrent\r\n        set(value) {\r\n            _iptvSourceCurrent = value\r\n            Configs.iptvSourceCurrent = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _iptvSourceList by mutableStateOf(IptvSourceList())\r\n    var iptvSourceList: IptvSourceList\r\n        get() = _iptvSourceList\r\n        set(value) {\r\n            _iptvSourceList = value\r\n            Configs.iptvSourceList = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _iptvChannelGroupHiddenList by mutableStateOf(emptySet<String>())\r\n    var iptvChannelGroupHiddenList: Set<String>\r\n        get() = _iptvChannelGroupHiddenList\r\n        set(value) {\r\n            _iptvChannelGroupHiddenList = value\r\n            Configs.iptvChannelGroupHiddenList = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _iptvHybridMode by mutableStateOf(Configs.IptvHybridMode.DISABLE)\r\n    var iptvHybridMode: Configs.IptvHybridMode\r\n        get() = _iptvHybridMode\r\n        set(value) {\r\n            _iptvHybridMode = value\r\n            Configs.iptvHybridMode = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _iptvSimilarChannelMerge by mutableStateOf(false)\r\n    var iptvSimilarChannelMerge: Boolean\r\n        get() = _iptvSimilarChannelMerge\r\n        set(value) {\r\n            _iptvSimilarChannelMerge = value\r\n            Configs.iptvSimilarChannelMerge = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _iptvChannelLogoProvider by mutableStateOf(\"\")\r\n    var iptvChannelLogoProvider: String\r\n        get() = _iptvChannelLogoProvider\r\n        set(value) {\r\n            _iptvChannelLogoProvider = value\r\n            Configs.iptvChannelLogoProvider = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _iptvChannelLogoOverride by mutableStateOf(false)\r\n    var iptvChannelLogoOverride: Boolean\r\n        get() = _iptvChannelLogoOverride\r\n        set(value) {\r\n            _iptvChannelLogoOverride = value\r\n            Configs.iptvChannelLogoOverride = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _iptvChannelFavoriteEnable by mutableStateOf(false)\r\n    var iptvChannelFavoriteEnable: Boolean\r\n        get() = _iptvChannelFavoriteEnable\r\n        set(value) {\r\n            _iptvChannelFavoriteEnable = value\r\n            Configs.iptvChannelFavoriteEnable = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _iptvChannelFavoriteListVisible by mutableStateOf(false)\r\n    var iptvChannelFavoriteListVisible: Boolean\r\n        get() = _iptvChannelFavoriteListVisible\r\n        set(value) {\r\n            _iptvChannelFavoriteListVisible = value\r\n            Configs.iptvChannelFavoriteListVisible = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _iptvChannelFavoriteList by mutableStateOf(ChannelFavoriteList())\r\n    var iptvChannelFavoriteList: ChannelFavoriteList\r\n        get() = _iptvChannelFavoriteList\r\n        set(value) {\r\n            _iptvChannelFavoriteList = value\r\n            Configs.iptvChannelFavoriteList = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _iptvChannelLastPlay by mutableStateOf(Channel.EMPTY)\r\n    var iptvChannelLastPlay: Channel\r\n        get() = _iptvChannelLastPlay\r\n        set(value) {\r\n            _iptvChannelLastPlay = value\r\n            Configs.iptvChannelLastPlay = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _iptvChannelLinePlayableHostList by mutableStateOf(emptySet<String>())\r\n    var iptvChannelLinePlayableHostList: Set<String>\r\n        get() = _iptvChannelLinePlayableHostList\r\n        set(value) {\r\n            _iptvChannelLinePlayableHostList = value\r\n            Configs.iptvChannelLinePlayableHostList = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _iptvChannelLinePlayableUrlList by mutableStateOf(emptySet<String>())\r\n    var iptvChannelLinePlayableUrlList: Set<String>\r\n        get() = _iptvChannelLinePlayableUrlList\r\n        set(value) {\r\n            _iptvChannelLinePlayableUrlList = value\r\n            Configs.iptvChannelLinePlayableUrlList = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _iptvChannelChangeFlip by mutableStateOf(false)\r\n    var iptvChannelChangeFlip: Boolean\r\n        get() = _iptvChannelChangeFlip\r\n        set(value) {\r\n            _iptvChannelChangeFlip = value\r\n            Configs.iptvChannelChangeFlip = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _iptvChannelNoSelectEnable by mutableStateOf(false)\r\n    var iptvChannelNoSelectEnable: Boolean\r\n        get() = _iptvChannelNoSelectEnable\r\n        set(value) {\r\n            _iptvChannelNoSelectEnable = value\r\n            Configs.iptvChannelNoSelectEnable = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _iptvChannelChangeListLoop by mutableStateOf(false)\r\n    var iptvChannelChangeListLoop: Boolean\r\n        get() = _iptvChannelChangeListLoop\r\n        set(value) {\r\n            _iptvChannelChangeListLoop = value\r\n            Configs.iptvChannelChangeListLoop = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _epgEnable by mutableStateOf(false)\r\n    var epgEnable: Boolean\r\n        get() = _epgEnable\r\n        set(value) {\r\n            _epgEnable = value\r\n            Configs.epgEnable = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _epgSourceCurrent by mutableStateOf(EpgSource())\r\n    var epgSourceCurrent: EpgSource\r\n        get() = _epgSourceCurrent\r\n        set(value) {\r\n            _epgSourceCurrent = value\r\n            Configs.epgSourceCurrent = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _epgSourceList by mutableStateOf(EpgSourceList())\r\n    var epgSourceList: EpgSourceList\r\n        get() = _epgSourceList\r\n        set(value) {\r\n            _epgSourceList = value\r\n            Configs.epgSourceList = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _epgRefreshTimeThreshold by mutableIntStateOf(0)\r\n    var epgRefreshTimeThreshold: Int\r\n        get() = _epgRefreshTimeThreshold\r\n        set(value) {\r\n            _epgRefreshTimeThreshold = value\r\n            Configs.epgRefreshTimeThreshold = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _epgSourceFollowIptv by mutableStateOf(false)\r\n    var epgSourceFollowIptv: Boolean\r\n        get() = _epgSourceFollowIptv\r\n        set(value) {\r\n            _epgSourceFollowIptv = value\r\n            Configs.epgSourceFollowIptv = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _epgChannelReserveList by mutableStateOf(EpgProgrammeReserveList())\r\n    var epgChannelReserveList: EpgProgrammeReserveList\r\n        get() = _epgChannelReserveList\r\n        set(value) {\r\n            _epgChannelReserveList = value\r\n            Configs.epgChannelReserveList = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _uiShowEpgProgrammeProgress by mutableStateOf(false)\r\n    var uiShowEpgProgrammeProgress: Boolean\r\n        get() = _uiShowEpgProgrammeProgress\r\n        set(value) {\r\n            _uiShowEpgProgrammeProgress = value\r\n            Configs.uiShowEpgProgrammeProgress = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _uiShowEpgProgrammePermanentProgress by mutableStateOf(false)\r\n    var uiShowEpgProgrammePermanentProgress: Boolean\r\n        get() = _uiShowEpgProgrammePermanentProgress\r\n        set(value) {\r\n            _uiShowEpgProgrammePermanentProgress = value\r\n            Configs.uiShowEpgProgrammePermanentProgress = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _uiShowChannelLogo by mutableStateOf(true)\r\n    var uiShowChannelLogo: Boolean\r\n        get() = _uiShowChannelLogo\r\n        set(value) {\r\n            _uiShowChannelLogo = value\r\n            Configs.uiShowChannelLogo = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _uiShowChannelPreview by mutableStateOf(false)\r\n    var uiShowChannelPreview: Boolean\r\n        get() = _uiShowChannelPreview\r\n        set(value) {\r\n            _uiShowChannelPreview = value\r\n            Configs.uiShowChannelPreview = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _uiUseClassicPanelScreen by mutableStateOf(false)\r\n    var uiUseClassicPanelScreen: Boolean\r\n        get() = _uiUseClassicPanelScreen\r\n        set(value) {\r\n            _uiUseClassicPanelScreen = value\r\n            Configs.uiUseClassicPanelScreen = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _uiDensityScaleRatio by mutableFloatStateOf(0f)\r\n    var uiDensityScaleRatio: Float\r\n        get() = _uiDensityScaleRatio\r\n        set(value) {\r\n            _uiDensityScaleRatio = value\r\n            Configs.uiDensityScaleRatio = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _uiFontScaleRatio by mutableFloatStateOf(1f)\r\n    var uiFontScaleRatio: Float\r\n        get() = _uiFontScaleRatio\r\n        set(value) {\r\n            _uiFontScaleRatio = value\r\n            Configs.uiFontScaleRatio = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _uiTimeShowMode by mutableStateOf(Configs.UiTimeShowMode.HIDDEN)\r\n    var uiTimeShowMode: Configs.UiTimeShowMode\r\n        get() = _uiTimeShowMode\r\n        set(value) {\r\n            _uiTimeShowMode = value\r\n            Configs.uiTimeShowMode = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _uiFocusOptimize by mutableStateOf(false)\r\n    var uiFocusOptimize: Boolean\r\n        get() = _uiFocusOptimize\r\n        set(value) {\r\n            _uiFocusOptimize = value\r\n            Configs.uiFocusOptimize = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _uiScreenAutoCloseDelay by mutableLongStateOf(0)\r\n    var uiScreenAutoCloseDelay: Long\r\n        get() = _uiScreenAutoCloseDelay\r\n        set(value) {\r\n            _uiScreenAutoCloseDelay = value\r\n            Configs.uiScreenAutoCloseDelay = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _updateForceRemind by mutableStateOf(false)\r\n    var updateForceRemind: Boolean\r\n        get() = _updateForceRemind\r\n        set(value) {\r\n            _updateForceRemind = value\r\n            Configs.updateForceRemind = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _updateChannel by mutableStateOf(\"\")\r\n    var updateChannel: String\r\n        get() = _updateChannel\r\n        set(value) {\r\n            _updateChannel = value\r\n            Configs.updateChannel = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _videoPlayerCore by mutableStateOf(Configs.VideoPlayerCore.MEDIA3)\r\n    var videoPlayerCore: Configs.VideoPlayerCore\r\n        get() = _videoPlayerCore\r\n        set(value) {\r\n            _videoPlayerCore = value\r\n            Configs.videoPlayerCore = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _videoPlayerRenderMode by mutableStateOf(Configs.VideoPlayerRenderMode.SURFACE_VIEW)\r\n    var videoPlayerRenderMode: Configs.VideoPlayerRenderMode\r\n        get() = _videoPlayerRenderMode\r\n        set(value) {\r\n            _videoPlayerRenderMode = value\r\n            Configs.videoPlayerRenderMode = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _videoPlayerUserAgent by mutableStateOf(\"\")\r\n    var videoPlayerUserAgent: String\r\n        get() = _videoPlayerUserAgent\r\n        set(value) {\r\n            _videoPlayerUserAgent = value\r\n            Configs.videoPlayerUserAgent = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _videoPlayerHeaders by mutableStateOf(\"\")\r\n    var videoPlayerHeaders: String\r\n        get() = _videoPlayerHeaders\r\n        set(value) {\r\n            _videoPlayerHeaders = value\r\n            Configs.videoPlayerHeaders = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _videoPlayerLoadTimeout by mutableLongStateOf(0)\r\n    var videoPlayerLoadTimeout: Long\r\n        get() = _videoPlayerLoadTimeout\r\n        set(value) {\r\n            _videoPlayerLoadTimeout = value\r\n            Configs.videoPlayerLoadTimeout = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _videoPlayerDisplayMode by mutableStateOf(VideoPlayerDisplayMode.ORIGINAL)\r\n    var videoPlayerDisplayMode: VideoPlayerDisplayMode\r\n        get() = _videoPlayerDisplayMode\r\n        set(value) {\r\n            _videoPlayerDisplayMode = value\r\n            Configs.videoPlayerDisplayMode = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _videoPlayerForceAudioSoftDecode by mutableStateOf(false)\r\n    var videoPlayerForceAudioSoftDecode: Boolean\r\n        get() = _videoPlayerForceAudioSoftDecode\r\n        set(value) {\r\n            _videoPlayerForceAudioSoftDecode = value\r\n            Configs.videoPlayerForceAudioSoftDecode = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _videoPlayerStopPreviousMediaItem by mutableStateOf(false)\r\n    var videoPlayerStopPreviousMediaItem: Boolean\r\n        get() = _videoPlayerStopPreviousMediaItem\r\n        set(value) {\r\n            _videoPlayerStopPreviousMediaItem = value\r\n            Configs.videoPlayerStopPreviousMediaItem = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _videoPlayerSkipMultipleFramesOnSameVSync by mutableStateOf(false)\r\n    var videoPlayerSkipMultipleFramesOnSameVSync: Boolean\r\n        get() = _videoPlayerSkipMultipleFramesOnSameVSync\r\n        set(value) {\r\n            _videoPlayerSkipMultipleFramesOnSameVSync = value\r\n            Configs.videoPlayerSkipMultipleFramesOnSameVSync = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _themeAppCurrent by mutableStateOf<AppThemeDef?>(null)\r\n    var themeAppCurrent: AppThemeDef?\r\n        get() = _themeAppCurrent\r\n        set(value) {\r\n            _themeAppCurrent = value\r\n            Configs.themeAppCurrent = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private var _cloudSyncAutoPull by mutableStateOf(false)\r\n    var cloudSyncAutoPull: Boolean\r\n        get() = _cloudSyncAutoPull\r\n        set(value) {\r\n            _cloudSyncAutoPull = value\r\n            Configs.cloudSyncAutoPull = value\r\n        }\r\n\r\n    private var _cloudSyncProvider by mutableStateOf(CloudSyncProvider.GITHUB_GIST)\r\n    var cloudSyncProvider: CloudSyncProvider\r\n        get() = _cloudSyncProvider\r\n        set(value) {\r\n            _cloudSyncProvider = value\r\n            Configs.cloudSyncProvider = value\r\n        }\r\n\r\n    private var _cloudSyncGithubGistId by mutableStateOf(\"\")\r\n    var cloudSyncGithubGistId: String\r\n        get() = _cloudSyncGithubGistId\r\n        set(value) {\r\n            _cloudSyncGithubGistId = value\r\n            Configs.cloudSyncGithubGistId = value\r\n        }\r\n\r\n    private var _cloudSyncGithubGistToken by mutableStateOf(\"\")\r\n    var cloudSyncGithubGistToken: String\r\n        get() = _cloudSyncGithubGistToken\r\n        set(value) {\r\n            _cloudSyncGithubGistToken = value\r\n            Configs.cloudSyncGithubGistToken = value\r\n        }\r\n\r\n    private var _cloudSyncGiteeGistId by mutableStateOf(\"\")\r\n    var cloudSyncGiteeGistId: String\r\n        get() = _cloudSyncGiteeGistId\r\n        set(value) {\r\n            _cloudSyncGiteeGistId = value\r\n            Configs.cloudSyncGiteeGistId = value\r\n        }\r\n\r\n    private var _cloudSyncGiteeGistToken by mutableStateOf(\"\")\r\n    var cloudSyncGiteeGistToken: String\r\n        get() = _cloudSyncGiteeGistToken\r\n        set(value) {\r\n            _cloudSyncGiteeGistToken = value\r\n            Configs.cloudSyncGiteeGistToken = value\r\n        }\r\n\r\n    private var _cloudSyncNetworkUrl by mutableStateOf(\"\")\r\n    var cloudSyncNetworkUrl: String\r\n        get() = _cloudSyncNetworkUrl\r\n        set(value) {\r\n            _cloudSyncNetworkUrl = value\r\n            Configs.cloudSyncNetworkUrl = value\r\n        }\r\n\r\n    private var _cloudSyncLocalFilePath by mutableStateOf(\"\")\r\n    var cloudSyncLocalFilePath: String\r\n        get() = _cloudSyncLocalFilePath\r\n        set(value) {\r\n            _cloudSyncLocalFilePath = value\r\n            Configs.cloudSyncLocalFilePath = value\r\n        }\r\n\r\n    private var _cloudSyncWebDavUrl by mutableStateOf(\"\")\r\n    var cloudSyncWebDavUrl: String\r\n        get() = _cloudSyncWebDavUrl\r\n        set(value) {\r\n            _cloudSyncWebDavUrl = value\r\n            Configs.cloudSyncWebDavUrl = value\r\n        }\r\n\r\n    private var _cloudSyncWebDavUsername by mutableStateOf(\"\")\r\n    var cloudSyncWebDavUsername: String\r\n        get() = _cloudSyncWebDavUsername\r\n        set(value) {\r\n            _cloudSyncWebDavUsername = value\r\n            Configs.cloudSyncWebDavUsername = value\r\n        }\r\n\r\n    private var _cloudSyncWebDavPassword by mutableStateOf(\"\")\r\n    var cloudSyncWebDavPassword: String\r\n        get() = _cloudSyncWebDavPassword\r\n        set(value) {\r\n            _cloudSyncWebDavPassword = value\r\n            Configs.cloudSyncWebDavPassword = value\r\n        }\r\n\r\n    private var _feiyangAllInOneFilePath by mutableStateOf(\"\")\r\n    var feiyangAllInOneFilePath: String\r\n        get() = _feiyangAllInOneFilePath\r\n        set(value) {\r\n            _feiyangAllInOneFilePath = value\r\n            Configs.feiyangAllInOneFilePath = value\r\n            afterSetWhenCloudSyncAutoPull()\r\n        }\r\n\r\n    private fun afterSetWhenCloudSyncAutoPull() {\r\n        // if (_cloudSyncAutoPull) Snackbar.show(\"云同步：自动拉取已启用\")\r\n    }\r\n\r\n    init {\r\n        runCatching { refresh() }\r\n\r\n        // 删除过期的预约\r\n        _epgChannelReserveList = EpgProgrammeReserveList(\r\n            _epgChannelReserveList.filter {\r\n                System.currentTimeMillis() < it.startAt + 60 * 1000\r\n            }\r\n        )\r\n    }\r\n\r\n    fun refresh() {\r\n        _appBootLaunch = Configs.appBootLaunch\r\n        _appPipEnable = Configs.appPipEnable\r\n        _appLastLatestVersion = Configs.appLastLatestVersion\r\n        _appAgreementAgreed = Configs.appAgreementAgreed\r\n        _appStartupScreen = Configs.appStartupScreen\r\n        _debugDeveloperMode = Configs.debugDeveloperMode\r\n        _debugShowFps = Configs.debugShowFps\r\n        _debugShowVideoPlayerMetadata = Configs.debugShowVideoPlayerMetadata\r\n        _debugShowLayoutGrids = Configs.debugShowLayoutGrids\r\n        _iptvSourceCacheTime = Configs.iptvSourceCacheTime\r\n        _iptvSourceCurrent = Configs.iptvSourceCurrent\r\n        _iptvSourceList = Configs.iptvSourceList\r\n        _iptvChannelGroupHiddenList = Configs.iptvChannelGroupHiddenList\r\n        _iptvHybridMode = Configs.iptvHybridMode\r\n        _iptvSimilarChannelMerge = Configs.iptvSimilarChannelMerge\r\n        _iptvChannelLogoProvider = Configs.iptvChannelLogoProvider\r\n        _iptvChannelLogoOverride = Configs.iptvChannelLogoOverride\r\n        _iptvChannelFavoriteEnable = Configs.iptvChannelFavoriteEnable\r\n        _iptvChannelFavoriteListVisible = Configs.iptvChannelFavoriteListVisible\r\n        _iptvChannelFavoriteList = Configs.iptvChannelFavoriteList\r\n        _iptvChannelLastPlay = Configs.iptvChannelLastPlay\r\n        _iptvChannelLinePlayableHostList = Configs.iptvChannelLinePlayableHostList\r\n        _iptvChannelLinePlayableUrlList = Configs.iptvChannelLinePlayableUrlList\r\n        _iptvChannelChangeFlip = Configs.iptvChannelChangeFlip\r\n        _iptvChannelNoSelectEnable = Configs.iptvChannelNoSelectEnable\r\n        _iptvChannelChangeListLoop = Configs.iptvChannelChangeListLoop\r\n        _epgEnable = Configs.epgEnable\r\n        _epgSourceCurrent = Configs.epgSourceCurrent\r\n        _epgSourceList = Configs.epgSourceList\r\n        _epgRefreshTimeThreshold = Configs.epgRefreshTimeThreshold\r\n        _epgSourceFollowIptv = Configs.epgSourceFollowIptv\r\n        _epgChannelReserveList = Configs.epgChannelReserveList\r\n        _uiShowEpgProgrammeProgress = Configs.uiShowEpgProgrammeProgress\r\n        _uiShowEpgProgrammePermanentProgress = Configs.uiShowEpgProgrammePermanentProgress\r\n        _uiShowChannelLogo = Configs.uiShowChannelLogo\r\n        _uiShowChannelPreview = Configs.uiShowChannelPreview\r\n        _uiUseClassicPanelScreen = Configs.uiUseClassicPanelScreen\r\n        _uiDensityScaleRatio = Configs.uiDensityScaleRatio\r\n        _uiFontScaleRatio = Configs.uiFontScaleRatio\r\n        _uiTimeShowMode = Configs.uiTimeShowMode\r\n        _uiFocusOptimize = Configs.uiFocusOptimize\r\n        _uiScreenAutoCloseDelay = Configs.uiScreenAutoCloseDelay\r\n        _updateForceRemind = Configs.updateForceRemind\r\n        _updateChannel = Configs.updateChannel\r\n        _videoPlayerCore = Configs.videoPlayerCore\r\n        _videoPlayerRenderMode = Configs.videoPlayerRenderMode\r\n        _videoPlayerUserAgent = Configs.videoPlayerUserAgent\r\n        _videoPlayerHeaders = Configs.videoPlayerHeaders\r\n        _videoPlayerLoadTimeout = Configs.videoPlayerLoadTimeout\r\n        _videoPlayerDisplayMode = Configs.videoPlayerDisplayMode\r\n        _videoPlayerForceAudioSoftDecode = Configs.videoPlayerForceAudioSoftDecode\r\n        _videoPlayerStopPreviousMediaItem = Configs.videoPlayerStopPreviousMediaItem\r\n        _videoPlayerSkipMultipleFramesOnSameVSync = Configs.videoPlayerSkipMultipleFramesOnSameVSync\r\n        _themeAppCurrent = Configs.themeAppCurrent\r\n        _cloudSyncAutoPull = Configs.cloudSyncAutoPull\r\n        _cloudSyncProvider = Configs.cloudSyncProvider\r\n        _cloudSyncGithubGistId = Configs.cloudSyncGithubGistId\r\n        _cloudSyncGithubGistToken = Configs.cloudSyncGithubGistToken\r\n        _cloudSyncGiteeGistId = Configs.cloudSyncGiteeGistId\r\n        _cloudSyncGiteeGistToken = Configs.cloudSyncGiteeGistToken\r\n        _cloudSyncNetworkUrl = Configs.cloudSyncNetworkUrl\r\n        _cloudSyncLocalFilePath = Configs.cloudSyncLocalFilePath\r\n        _cloudSyncWebDavUrl = Configs.cloudSyncWebDavUrl\r\n        _cloudSyncWebDavUsername = Configs.cloudSyncWebDavUsername\r\n        _cloudSyncWebDavPassword = Configs.cloudSyncWebDavPassword\r\n        _feiyangAllInOneFilePath = Configs.feiyangAllInOneFilePath\r\n    }\r\n\r\n    companion object {\r\n        var instance: SettingsViewModel? = null\r\n    }\r\n}\r\n\r\nval settingsVM: SettingsViewModel\r\n    @Composable get() = SettingsViewModel.instance ?: viewModel<SettingsViewModel>().also {\r\n        SettingsViewModel.instance = it\r\n    }
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/tv/src/main/java/top/yogiczy/mytv/tv/ui/screen/settings/SettingsViewModel.kt b/tv/src/main/java/top/yogiczy/mytv/tv/ui/screen/settings/SettingsViewModel.kt
--- a/tv/src/main/java/top/yogiczy/mytv/tv/ui/screen/settings/SettingsViewModel.kt	(revision ****************************************)
+++ b/tv/src/main/java/top/yogiczy/mytv/tv/ui/screen/settings/SettingsViewModel.kt	(date 1755499320804)
@@ -596,16 +596,6 @@
             _cloudSyncWebDavPassword = value
             Configs.cloudSyncWebDavPassword = value
         }
-
-    private var _feiyangAllInOneFilePath by mutableStateOf("")
-    var feiyangAllInOneFilePath: String
-        get() = _feiyangAllInOneFilePath
-        set(value) {
-            _feiyangAllInOneFilePath = value
-            Configs.feiyangAllInOneFilePath = value
-            afterSetWhenCloudSyncAutoPull()
-        }
-
     private fun afterSetWhenCloudSyncAutoPull() {
         // if (_cloudSyncAutoPull) Snackbar.show("云同步：自动拉取已启用")
     }
@@ -687,7 +677,6 @@
         _cloudSyncWebDavUrl = Configs.cloudSyncWebDavUrl
         _cloudSyncWebDavUsername = Configs.cloudSyncWebDavUsername
         _cloudSyncWebDavPassword = Configs.cloudSyncWebDavPassword
-        _feiyangAllInOneFilePath = Configs.feiyangAllInOneFilePath
     }
 
     companion object {
Index: tv/src/main/res/raw/web_push.html
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+><!DOCTYPE html>\r\n<html>\r\n\r\n<head>\r\n    <meta charset=\"utf-8\">\r\n    <meta content=\"width=device-width, initial-scale=1\" name=\"viewport\">\r\n    <title>天光云影</title>\r\n    <link href=\"./web_push_css.css\" rel=\"stylesheet\" />\r\n    <script src=\"./web_push_js.js\"></script>\r\n\r\n    <style>\r\n        .van-theme-dark body {\r\n            color: #f5f5f5;\r\n            background-color: black;\r\n        }\r\n    </style>\r\n</head>\r\n\r\n<body>\r\n    <div class=\"min-h-100vh pt-46px pb-66px\" id=\"app\">\r\n        <van-config-provider :theme=\"isDark ? 'dark' : undefined\">\r\n            <template v-if=\"info\">\r\n                <div class=\"p-20px pt-0\">\r\n                    <div class=\"ml-16px text-32px\">{{ info.appTitle }}</div>\r\n                    <div class=\"ml-16px text-gray text-14px\">{{ info.appRepo }}</div>\r\n                </div>\r\n\r\n                <template v-if=\"tabActive === 'config'\">\r\n                    <van-cell-group inset>\r\n                        <van-cell title=\"点击进入高级模式\" is-link url=\"/advance\"></van-cell>\r\n                    </van-cell-group>\r\n\r\n                    <van-cell-group inset title=\"直播源\">\r\n                        <van-cell title=\"自定义直播源\">\r\n                            <template #label>\r\n                                <van-space class=\"w-full\" direction=\"vertical\" size=\"small\">\r\n                                    <span>支持m3u、txt格式</span>\r\n\r\n                                    <van-field class=\"!pl-0\" input-align=\"right\" label=\"类型\">\r\n                                        <template #input>\r\n                                            <van-radio-group direction=\"horizontal\" v-model=\"iptvSource.type\">\r\n                                                <van-radio name=\"url\">远程</van-radio>\r\n                                                <van-radio name=\"file\">文件</van-radio>\r\n                                                <van-radio name=\"content\">静态</van-radio>\r\n                                            </van-radio-group>\r\n                                        </template>\r\n                                    </van-field>\r\n\r\n                                    <van-field class=\"!pl-0\" input-align=\"right\" label=\"名称\" placeholder=\"直播源名称\"\r\n                                        v-model=\"iptvSource.name\"></van-field>\r\n\r\n                                    <van-field class=\"!pl-0\" input-align=\"right\" label=\"链接\" placeholder=\"直播源链接\"\r\n                                        v-if=\"iptvSource.type === 'url'\" v-model=\"iptvSource.url\"></van-field>\r\n\r\n                                    <van-field class=\"!pl-0\" input-align=\"right\" label=\"文件路径\" placeholder=\"直播源文件路径\"\r\n                                        v-else-if=\"iptvSource.type === 'file'\"\r\n                                        v-model=\"iptvSource.filePath\"></van-field>\r\n\r\n                                    <template v-else-if=\"iptvSource.type === 'content'\">\r\n                                        <van-field :input-align=\"iptvSource.content ? 'left' : 'right'\" class=\"!pl-0\"\r\n                                            label=\"内容\" placeholder=\"直播源内容\" rows=\"5\" type=\"textarea\"\r\n                                            v-model=\"iptvSource.content\"></van-field>\r\n\r\n                                        <van-field class=\"!pl-0\" input-align=\"right\" label=\"上传\">\r\n                                            <template #input>\r\n                                                <van-uploader :after-read=\"uploadIptvSourceContentAfterRead\"\r\n                                                    accept=\".txt,.m3u\" />\r\n                                            </template>\r\n                                        </van-field>\r\n                                    </template>\r\n\r\n                                    <div class=\"flex justify-end\">\r\n                                        <van-button @click=\"pushIptvSource\" size=\"small\" type=\"primary\">\r\n                                            推送直播源\r\n                                        </van-button>\r\n                                    </div>\r\n                                </van-space>\r\n                            </template>\r\n                        </van-cell>\r\n\r\n                        <van-cell title=\"频道图标提供\">\r\n                            <template #label>\r\n                                <van-space class=\"w-full\" direction=\"vertical\" size=\"small\">\r\n                                    <span>格式：{name} - 保持不变，{name|lowercase} - 小写，{name|uppercase} - 大写</span>\r\n\r\n                                    <van-field class=\"!p-0\" placeholder=\"https://live.fanmingming.com/tv/{name}.png\"\r\n                                        v-model=\"configs.iptvChannelLogoProvider\"></van-field>\r\n\r\n                                    <div class=\"flex justify-end\">\r\n                                        <van-button @click=\"pushConfigs\" size=\"small\" type=\"primary\">\r\n                                            推送\r\n                                        </van-button>\r\n                                    </div>\r\n                                </van-space>\r\n                            </template>\r\n                        </van-cell>\r\n\r\n                        <van-cell title=\"频道别名\">\r\n                            <template #label>\r\n                                <van-space class=\"w-full\" direction=\"vertical\" size=\"small\">\r\n                                    <van-field :placeholder=\"channelAliasExample\" class=\"!p-0\" rows=\"5\" type=\"textarea\"\r\n                                        v-model=\"channelAlias\"></van-field>\r\n\r\n                                    <div class=\"flex justify-end\">\r\n                                        <van-button @click=\"updateChannelAlias\" size=\"small\" type=\"primary\">\r\n                                            推送\r\n                                        </van-button>\r\n                                    </div>\r\n                                </van-space></template>\r\n                        </van-cell>\r\n                    </van-cell-group>\r\n\r\n                    <van-cell-group inset title=\"节目单\">\r\n                        <van-cell title=\"自定义节目单\">\r\n                            <template #label>\r\n                                <van-space class=\"w-full\" direction=\"vertical\" size=\"small\">\r\n                                    <span>支持xml、xml.gz格式</span>\r\n\r\n                                    <van-field class=\"!pl-0\" input-align=\"right\" label=\"名称\" placeholder=\"节目单名称\"\r\n                                        v-model=\"epgSource.name\"></van-field>\r\n\r\n                                    <van-field class=\"!pl-0\" input-align=\"right\" label=\"链接\" placeholder=\"节目单链接\"\r\n                                        v-model=\"epgSource.url\"></van-field>\r\n\r\n                                    <div class=\"flex justify-end\">\r\n                                        <van-button @click=\"pushEpgSource\" size=\"small\" type=\"primary\">\r\n                                            推送节目单\r\n                                        </van-button>\r\n                                    </div>\r\n                                </van-space>\r\n                            </template>\r\n                        </van-cell>\r\n                    </van-cell-group>\r\n\r\n                    <van-cell-group inset title=\"播放器\">\r\n                        <van-cell title=\"自定义ua\">\r\n                            <template #label>\r\n                                <van-space class=\"w-full\" direction=\"vertical\" size=\"small\">\r\n                                    <van-field class=\"!p-0\" placeholder=\"播放器自定义ua\"\r\n                                        v-model=\"configs.videoPlayerUserAgent\"></van-field>\r\n\r\n                                    <div class=\"flex justify-end\">\r\n                                        <van-button @click=\"pushConfigs\" size=\"small\" type=\"primary\">\r\n                                            推送\r\n                                        </van-button>\r\n                                    </div>\r\n                                </van-space>\r\n                            </template>\r\n                        </van-cell>\r\n\r\n                        <van-cell title=\"自定义headers\">\r\n                            <template #label>\r\n                                <van-space class=\"w-full\" direction=\"vertical\" size=\"small\">\r\n                                    <van-field :placeholder=\"videoPlayerHeadersExample\" autosize class=\"!p-0\" rows=\"3\"\r\n                                        type=\"textarea\" v-model=\"configs.videoPlayerHeaders\"></van-field>\r\n\r\n                                    <div class=\"flex justify-end\">\r\n                                        <van-button @click=\"pushConfigs\" size=\"small\" type=\"primary\">\r\n                                            推送\r\n                                        </van-button>\r\n                                    </div>\r\n                                </van-space>\r\n                            </template>\r\n                        </van-cell>\r\n                    </van-cell-group>\r\n\r\n                    <van-cell-group inset title=\"云同步\">\r\n                        <van-cell title=\"服务商\">\r\n                            <template #label>\r\n                                <van-radio-group direction=\"horizontal\" v-model=\"configs.cloudSyncProvider\">\r\n                                    <van-radio name=\"GITHUB_GIST\">GitHub Gist</van-radio>\r\n                                    <van-radio name=\"GITEE_GIST\">Gitee 代码片段</van-radio>\r\n                                    <van-radio name=\"NETWORK_URL\">网络链接</van-radio>\r\n                                    <van-radio name=\"LOCAL_FILE\">本地文件</van-radio>\r\n                                    <van-radio name=\"WEBDAV\">WebDAV</van-radio>\r\n                                </van-radio-group>\r\n                            </template>\r\n                        </van-cell>\r\n\r\n                        <template v-if=\"configs.cloudSyncProvider === 'GITHUB_GIST'\">\r\n                            <van-cell title=\"Github Gist Id\">\r\n                                <template #label>\r\n                                    <van-field class=\"!p-0\" v-model=\"configs.cloudSyncGithubGistId\"></van-field>\r\n                                </template>\r\n                            </van-cell>\r\n\r\n                            <van-cell title=\"Github Gist Token\">\r\n                                <template #label>\r\n                                    <van-field class=\"!p-0\" v-model=\"configs.cloudSyncGithubGistToken\"></van-field>\r\n                                </template>\r\n                            </van-cell>\r\n                        </template>\r\n\r\n                        <template v-if=\"configs.cloudSyncProvider === 'GITEE_GIST'\">\r\n                            <van-cell title=\"Gitee 代码片段 Id\">\r\n                                <template #label>\r\n                                    <van-field class=\"!p-0\" v-model=\"configs.cloudSyncGiteeGistId\"></van-field>\r\n                                </template>\r\n                            </van-cell>\r\n\r\n                            <van-cell title=\"Gitee 代码片段 Token\">\r\n                                <template #label>\r\n                                    <van-field class=\"!p-0\" v-model=\"configs.cloudSyncGiteeGistToken\"></van-field>\r\n                                </template>\r\n                            </van-cell>\r\n                        </template>\r\n\r\n                        <template v-if=\"configs.cloudSyncProvider === 'NETWORK_URL'\">\r\n                            <van-cell title=\"网络链接\">\r\n                                <template #label>\r\n                                    <van-field class=\"!p-0\" v-model=\"configs.cloudSyncNetworkUrl\"></van-field>\r\n                                </template>\r\n                            </van-cell>\r\n                        </template>\r\n\r\n                        <template v-if=\"configs.cloudSyncProvider === 'LOCAL_FILE'\">\r\n                            <van-cell title=\"本地文件路径\">\r\n                                <template #label>\r\n                                    <van-field class=\"!p-0\" v-model=\"configs.cloudSyncLocalFilePath\"></van-field>\r\n                                </template>\r\n                            </van-cell>\r\n                        </template>\r\n\r\n                        <template v-if=\"configs.cloudSyncProvider === 'WEBDAV'\">\r\n                            <van-cell title=\"WebDAV 地址\">\r\n                                <template #label>\r\n                                    <van-field class=\"!p-0\" v-model=\"configs.cloudSyncWebDavUrl\"></van-field>\r\n                                </template>\r\n                            </van-cell>\r\n\r\n                            <van-cell title=\"WebDAV 用户名\">\r\n                                <template #label>\r\n                                    <van-field class=\"!p-0\" v-model=\"configs.cloudSyncWebDavUsername\"></van-field>\r\n                                </template>\r\n                            </van-cell>\r\n\r\n                            <van-cell title=\"WebDAV 密码\">\r\n                                <template #label>\r\n                                    <van-field class=\"!p-0\" v-model=\"configs.cloudSyncWebDavPassword\"></van-field>\r\n                                </template>\r\n                            </van-cell>\r\n                        </template>\r\n\r\n                        <van-cell>\r\n                            <div class=\"flex justify-end\">\r\n                                <van-button @click=\"pushConfigs\" size=\"small\" type=\"primary\">\r\n                                    推送\r\n                                </van-button>\r\n                            </div>\r\n                        </van-cell>\r\n                    </van-cell-group>\r\n\r\n                    <van-cell-group inset title=\"调试\">\r\n                        <van-cell title=\"上传apk\">\r\n                            <template #extra>\r\n                                <van-uploader :after-read=\"uploadApkAfterRead\" accept=\".apk\" />\r\n                            </template>\r\n                        </van-cell>\r\n                    </van-cell-group>\r\n\r\n                    <van-cell-group inset title=\"二进制\">\r\n                        <van-cell title=\"上传二进制\">\r\n                            <template #extra>\r\n                                <van-uploader :after-read=\"uploadAllInOneAfterRead\" accept=\"*.*\" />\r\n                            </template>\r\n                        </van-cell>\r\n\r\n                        <van-cell title=\"二进制文件地址\">\r\n                            <template #label>\r\n                                <van-field class=\"!p-0\" v-model=\"configs.feiyangAllInOneFilePath\"></van-field>\r\n                            </template>\r\n                        </van-cell>\r\n\r\n                        <van-cell>\r\n                            <div class=\"flex justify-end\">\r\n                                <van-button @click=\"pushConfigs\" size=\"small\" type=\"primary\">\r\n                                    推送\r\n                                </van-button>\r\n                            </div>\r\n                        </van-cell>\r\n                    </van-cell-group>\r\n                </template>\r\n\r\n                <template v-else-if=\"tabActive === 'log'\">\r\n                    <van-list>\r\n                        <van-cell :key=\"item.time\" :label=\"item.cause\" v-for=\"item in info.logHistory\">\r\n                            <template #title>\r\n                                <div class=\"flex flex-col gap-1\">\r\n                                    <div class=\"flex gap-1 items-center\">\r\n                                        <van-tag plain>{{ item.tag }}</van-tag>\r\n                                        <van-tag plain>{{ item.level }}</van-tag>\r\n                                    </div>\r\n                                    <span>{{ item.message }}</span>\r\n                                </div>\r\n                            </template>\r\n\r\n                            <template #extra>\r\n                                <span text=\"gray\">{{ dayjs(item.time).format('HH:mm:ss') }}</span>\r\n                            </template>\r\n                        </van-cell>\r\n                    </van-list>\r\n                </template>\r\n\r\n                <van-tabbar v-model=\"tabActive\">\r\n                    <van-tabbar-item icon=\"tv-o\" name=\"config\">配置</van-tabbar-item>\r\n                    <van-tabbar-item icon=\"list-switch\" name=\"log\">日志</van-tabbar-item>\r\n                </van-tabbar>\r\n            </template>\r\n\r\n            <van-empty image=\"network\" v-else />\r\n        </van-config-provider>\r\n    </div>\r\n\r\n    <script>\r\n        const { createApp, ref, onMounted, watch, nextTick } = Vue\r\n\r\n        // const baseUrl = \"http://127.0.0.1:10481\"\r\n        const baseUrl = \"\"\r\n\r\n        async function requestApi(url, config) {\r\n            const resp = await fetch(`${baseUrl}${url}`, config)\r\n            if (resp.status !== 200) {\r\n                throw new Error(`请求失败：${resp.status}`)\r\n            }\r\n\r\n            return resp\r\n        }\r\n\r\n        dayjs.locale('zh-cn')\r\n        dayjs.extend(dayjs_plugin_relativeTime)\r\n\r\n        createApp({\r\n            setup() {\r\n                const isDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches\r\n\r\n                const tabActive = ref('config')\r\n\r\n                const info = ref()\r\n                async function refreshInfo() {\r\n                    try {\r\n                        vant.showLoadingToast({ message: '加载中...', forbidClick: true, duration: 0 })\r\n                        info.value = await (await requestApi('/api/info')).json()\r\n                        info.value.logHistory = info.value.logHistory.reverse()\r\n                        vant.closeToast()\r\n                    } catch (e) {\r\n                        vant.showFailToast('无法获取信息')\r\n                        console.error(e)\r\n                    }\r\n                }\r\n\r\n                const configs = ref({})\r\n                async function refreshConfigs() {\r\n                    try {\r\n                        configs.value = await (await requestApi('/api/configs')).json()\r\n                    } catch (e) {\r\n                        vant.showFailToast('无法获取配置')\r\n                        console.error(e)\r\n                    }\r\n                }\r\n                async function pushConfigs() {\r\n                    try {\r\n                        vant.showLoadingToast({ message: '加载中...', forbidClick: true, duration: 0 })\r\n                        await requestApi('/api/configs', {\r\n                            method: \"POST\",\r\n                            body: JSON.stringify(configs.value),\r\n                            headers: { 'Content-Type': 'application/json' }\r\n                        })\r\n                        await refreshConfigs()\r\n                        vant.showSuccessToast(\"推送配置成功\")\r\n                    } catch (e) {\r\n                        vant.showFailToast('推送配置失败')\r\n                        console.error(e)\r\n                    }\r\n                }\r\n\r\n                const iptvSource = ref({\r\n                    name: `添加于${dayjs().format('YYYY-MM-DD HH:mm:ss')}`,\r\n                    type: 'url',\r\n                    url: '',\r\n                    filePath: '',\r\n                    content: '',\r\n                })\r\n                async function pushIptvSource() {\r\n                    if (!iptvSource.value.name) {\r\n                        vant.showFailToast('请填写直播源名称')\r\n                        return\r\n                    }\r\n\r\n                    if (!iptvSource.value.type === 'url' && !iptvSource.value.url) {\r\n                        vant.showFailToast('请填写直播源链接')\r\n                        return\r\n                    }\r\n\r\n                    if (iptvSource.value.type === 'file' && !iptvSource.value.filePath) {\r\n                        vant.showFailToast('请填写直播源文件路径')\r\n                        return\r\n                    }\r\n\r\n                    if (iptvSource.value.type === 'content' && !iptvSource.value.content) {\r\n                        vant.showFailToast('请填写直播源内容')\r\n                        return\r\n                    }\r\n\r\n                    try {\r\n                        vant.showLoadingToast({ message: '加载中...', forbidClick: true, duration: 0 })\r\n                        await requestApi('/api/iptv-source/push', {\r\n                            method: \"POST\",\r\n                            body: JSON.stringify({ ...iptvSource.value }),\r\n                            headers: { 'Content-Type': 'application/json' }\r\n                        })\r\n                        vant.showSuccessToast(\"推送直播源成功\")\r\n                    } catch (e) {\r\n                        vant.showFailToast('推送直播源失败')\r\n                        console.error(e)\r\n                    }\r\n                }\r\n                async function uploadIptvSourceContentAfterRead(file) {\r\n                    const reader = new FileReader();\r\n                    reader.onload = (e) => {\r\n                        const content = e.target.result;\r\n                        iptvSource.value.content = content;\r\n                    };\r\n                    reader.readAsText(file.file);\r\n                }\r\n\r\n                const epgSource = ref({ name: `添加于${dayjs().format('YYYY-MM-DD HH:mm:ss')}`, url: '' })\r\n                async function pushEpgSource() {\r\n                    if (!epgSource.value.name) {\r\n                        vant.showFailToast('请填写节目单名称')\r\n                        return\r\n                    }\r\n\r\n                    if (!epgSource.value.url) {\r\n                        vant.showFailToast('请填写节目单链接')\r\n                        return\r\n                    }\r\n\r\n                    try {\r\n                        vant.showLoadingToast({ message: '加载中...', forbidClick: true, duration: 0 })\r\n                        await requestApi('/api/epg-source/push', {\r\n                            method: \"POST\",\r\n                            body: JSON.stringify({ ...epgSource.value }),\r\n                            headers: { 'Content-Type': 'application/json' }\r\n                        })\r\n                        vant.showSuccessToast(\"推送节目单成功\")\r\n                    } catch (e) {\r\n                        vant.showFailToast('推送节目单失败')\r\n                        console.error(e)\r\n                    }\r\n                }\r\n\r\n                const channelAliasExample = `{\r\n    \"__suffix\": [\r\n        \"高码\",\r\n        \"HD\"\r\n    ],\r\n    \"频道1\": [\r\n        \"别名1\",\r\n        \"别名2\"\r\n    ]\r\n}`\r\n                const channelAlias = ref('')\r\n                async function refreshChannelAlias() {\r\n                    channelAlias.value = await (await requestApi('/api/channel-alias')).text()\r\n                }\r\n                async function updateChannelAlias() {\r\n                    try {\r\n                        vant.showLoadingToast({ message: '加载中...', forbidClick: true, duration: 0 })\r\n                        await requestApi('/api/channel-alias', {\r\n                            method: \"POST\",\r\n                            body: channelAlias.value,\r\n                        })\r\n                        vant.showSuccessToast(\"更新频道别名成功\")\r\n                        await refreshChannelAlias()\r\n                    } catch (e) {\r\n                        vant.showFailToast('更新频道别名失败')\r\n                        console.error(e)\r\n                    }\r\n                }\r\n\r\n                async function uploadApkAfterRead(file) {\r\n                    try {\r\n                        vant.showLoadingToast({ message: '加载中...', forbidClick: true, duration: 0 })\r\n                        const formData = new FormData()\r\n                        formData.append('filename', file.file)\r\n                        await requestApi('/api/upload/apk', { method: \"POST\", body: formData })\r\n                        vant.closeToast()\r\n                    } catch (e) {\r\n                        vant.showFailToast('上传apk失败')\r\n                        console.error(e)\r\n                    }\r\n                }\r\n\r\n                async function uploadAllInOneAfterRead(file) {\r\n                    try {\r\n                        vant.showLoadingToast({ message: '加载中...', forbidClick: true, duration: 0 })\r\n                        const formData = new FormData()\r\n                        formData.append('filename', file.file)\r\n                        await requestApi('/api/upload/allinone', { method: \"POST\", body: formData })\r\n                        vant.closeToast()\r\n                    } catch (e) {\r\n                        vant.showFailToast('上传AllInOne失败')\r\n                        console.error(e)\r\n                    }\r\n                }\r\n\r\n                const videoPlayerHeadersExample = \"Header-Name-1: Header-Value-1\\nHeader-Name-2: Header-Value-2\"\r\n\r\n                onMounted(async () => {\r\n                    await refreshInfo()\r\n                    await refreshChannelAlias()\r\n                    await refreshConfigs()\r\n                })\r\n\r\n                return {\r\n                    dayjs,\r\n                    isDark,\r\n                    tabActive,\r\n                    info,\r\n                    configs,\r\n                    pushConfigs,\r\n                    iptvSource,\r\n                    pushIptvSource,\r\n                    uploadIptvSourceContentAfterRead,\r\n                    epgSource,\r\n                    pushEpgSource,\r\n                    channelAliasExample,\r\n                    channelAlias,\r\n                    updateChannelAlias,\r\n                    uploadApkAfterRead,\r\n                    uploadAllInOneAfterRead,\r\n                    videoPlayerHeadersExample,\r\n                }\r\n            }\r\n        })\r\n            .use(vant)\r\n            .mount('#app')\r\n    </script>\r\n</body>\r\n\r\n</html>\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/tv/src/main/res/raw/web_push.html b/tv/src/main/res/raw/web_push.html
--- a/tv/src/main/res/raw/web_push.html	(revision ****************************************)
+++ b/tv/src/main/res/raw/web_push.html	(date 1755499320826)
@@ -257,28 +257,6 @@
                             </template>
                         </van-cell>
                     </van-cell-group>
-
-                    <van-cell-group inset title="二进制">
-                        <van-cell title="上传二进制">
-                            <template #extra>
-                                <van-uploader :after-read="uploadAllInOneAfterRead" accept="*.*" />
-                            </template>
-                        </van-cell>
-
-                        <van-cell title="二进制文件地址">
-                            <template #label>
-                                <van-field class="!p-0" v-model="configs.feiyangAllInOneFilePath"></van-field>
-                            </template>
-                        </van-cell>
-
-                        <van-cell>
-                            <div class="flex justify-end">
-                                <van-button @click="pushConfigs" size="small" type="primary">
-                                    推送
-                                </van-button>
-                            </div>
-                        </van-cell>
-                    </van-cell-group>
                 </template>
 
                 <template v-else-if="tabActive === 'log'">
@@ -491,19 +469,6 @@
                     }
                 }
 
-                async function uploadAllInOneAfterRead(file) {
-                    try {
-                        vant.showLoadingToast({ message: '加载中...', forbidClick: true, duration: 0 })
-                        const formData = new FormData()
-                        formData.append('filename', file.file)
-                        await requestApi('/api/upload/allinone', { method: "POST", body: formData })
-                        vant.closeToast()
-                    } catch (e) {
-                        vant.showFailToast('上传AllInOne失败')
-                        console.error(e)
-                    }
-                }
-
                 const videoPlayerHeadersExample = "Header-Name-1: Header-Value-1\nHeader-Name-2: Header-Value-2"
 
                 onMounted(async () => {
@@ -528,7 +493,6 @@
                     channelAlias,
                     updateChannelAlias,
                     uploadApkAfterRead,
-                    uploadAllInOneAfterRead,
                     videoPlayerHeadersExample,
                 }
             }
Index: README.md
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+><div align=\"center\">\r\n    <h1>天光云影</h1>\r\n<div align=\"center\">\r\n\r\n![GitHub Repo stars](https://img.shields.io/github/stars/yaoxieyoulei/mytv-android)\r\n![GitHub all releases](https://img.shields.io/github/downloads/yaoxieyoulei/mytv-android/total)\r\n[![Android Sdk Require](https://img.shields.io/badge/Android-5.0%2B-informational?logo=android)](https://apilevels.com/#:~:text=Jetpack%20Compose%20requires%20a%20minSdk%20of%2021%20or%20higher)\r\n[![GitHub](https://img.shields.io/github/license/yaoxieyoulei/mytv-android)](https://github.com/yaoxieyoulei/mytv-android)\r\n\r\n</div>\r\n    <p>使用Android原生开发的视频播放软件</p>\r\n</div>\r\n\r\n## 使用\r\n\r\n### 操作方式\r\n\r\n> 遥控器操作方式与主流视频播放软件类似；\r\n\r\n- 频道切换：使用上下方向键，或者数字键切换频道；屏幕上下滑动；\r\n- 频道选择：OK键；单击屏幕；\r\n- 线路切换：使用左右方向键；屏幕左右滑动；\r\n- 设置页面：按下菜单、帮助键，长按OK键；双击、长按屏幕；\r\n\r\n### 触摸键位对应\r\n\r\n- 方向键：屏幕上下左右滑动\r\n- OK键：点击屏幕\r\n- 长按OK键：长按屏幕\r\n- 菜单、帮助键：双击屏幕\r\n\r\n### 自定义设置\r\n\r\n- 访问以下网址：`http://<设备IP>:10481`\r\n\r\n## 下载\r\n\r\n可以通过右侧release进行下载或拉取代码到本地进行编译\r\n\r\n## 说明\r\n\r\n- 仅支持Android5及以上\r\n- 网络环境必须支持IPV6（默认订阅源）\r\n- 只在自家电视上测过，其他电视稳定性未知\r\n\r\n## 更新日志\r\n\r\n[更新日志](./CHANGELOG.md)\r\n\r\n## 声明\r\n\r\n此项目（天光云影）是个人为了兴趣而开发, 仅用于学习和测试。 所用API皆从官方网站收集, 不提供任何破解内容。\r\n\r\n## 技术交流\r\n\r\nTelegram: https://t.me/mytv_android\r\n\r\n## 赞赏\r\n\r\n<img src=\"./screenshots/mm_reward_qrcode.png\" width=\"48%\"/>\r\n\r\n## 致谢\r\n\r\n- [my-tv](https://github.com/lizongying/my-tv)\r\n- [参考设计稿](https://github.com/lizongying/my-tv/issues/594)\r\n- [live](https://github.com/fanmingming/live)\r\n- 等等\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/README.md b/README.md
--- a/README.md	(revision ****************************************)
+++ b/README.md	(date 1755498434466)
@@ -15,7 +15,7 @@
 
 ### 操作方式
 
-> 遥控器操作方式与主流视频播放软件类似；
+> 遥控器操作方式与主流视频播放软件类似； 
 
 - 频道切换：使用上下方向键，或者数字键切换频道；屏幕上下滑动；
 - 频道选择：OK键；单击屏幕；
Index: .idea/vcs.xml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+><?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n<project version=\"4\">\r\n  <component name=\"VcsDirectoryMappings\">\r\n    <mapping directory=\"\" vcs=\"Git\" />\r\n    <mapping directory=\"$PROJECT_DIR$/../YYKM\" vcs=\"Git\" />\r\n    <mapping directory=\"$PROJECT_DIR$/../media\" vcs=\"Git\" />\r\n  </component>\r\n</project>
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/.idea/vcs.xml b/.idea/vcs.xml
--- a/.idea/vcs.xml	(revision ****************************************)
+++ b/.idea/vcs.xml	(date 1755498434858)
@@ -2,7 +2,5 @@
 <project version="4">
   <component name="VcsDirectoryMappings">
     <mapping directory="" vcs="Git" />
-    <mapping directory="$PROJECT_DIR$/../YYKM" vcs="Git" />
-    <mapping directory="$PROJECT_DIR$/../media" vcs="Git" />
   </component>
 </project>
\ No newline at end of file
Index: .idea/gradle.xml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+><?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n<project version=\"4\">\r\n  <component name=\"GradleMigrationSettings\" migrationVersion=\"1\" />\r\n  <component name=\"GradleSettings\">\r\n    <option name=\"linkedExternalProjectsSettings\">\r\n      <GradleProjectSettings>\r\n        <option name=\"testRunner\" value=\"CHOOSE_PER_TEST\" />\r\n        <option name=\"externalProjectPath\" value=\"$PROJECT_DIR$\" />\r\n        <option name=\"gradleJvm\" value=\"#GRADLE_LOCAL_JAVA_HOME\" />\r\n        <option name=\"modules\">\r\n          <set>\r\n            <option value=\"$PROJECT_DIR$/../YYKM/tbsx5\" />\r\n            <option value=\"$PROJECT_DIR$/../media/libraries/cast\" />\r\n            <option value=\"$PROJECT_DIR$/../media/libraries/common\" />\r\n            <option value=\"$PROJECT_DIR$/../media/libraries/common_ktx\" />\r\n            <option value=\"$PROJECT_DIR$/../media/libraries/container\" />\r\n            <option value=\"$PROJECT_DIR$/../media/libraries/database\" />\r\n            <option value=\"$PROJECT_DIR$/../media/libraries/datasource\" />\r\n            <option value=\"$PROJECT_DIR$/../media/libraries/datasource_cronet\" />\r\n            <option value=\"$PROJECT_DIR$/../media/libraries/datasource_httpengine\" />\r\n            <option value=\"$PROJECT_DIR$/../media/libraries/datasource_okhttp\" />\r\n            <option value=\"$PROJECT_DIR$/../media/libraries/datasource_rtmp\" />\r\n            <option value=\"$PROJECT_DIR$/../media/libraries/decoder\" />\r\n            <option value=\"$PROJECT_DIR$/../media/libraries/decoder_av1\" />\r\n            <option value=\"$PROJECT_DIR$/../media/libraries/decoder_ffmpeg\" />\r\n            <option value=\"$PROJECT_DIR$/../media/libraries/decoder_flac\" />\r\n            <option value=\"$PROJECT_DIR$/../media/libraries/decoder_iamf\" />\r\n            <option value=\"$PROJECT_DIR$/../media/libraries/decoder_opus\" />\r\n            <option value=\"$PROJECT_DIR$/../media/libraries/decoder_vp9\" />\r\n            <option value=\"$PROJECT_DIR$/../media/libraries/effect\" />\r\n            <option value=\"$PROJECT_DIR$/../media/libraries/exoplayer\" />\r\n            <option value=\"$PROJECT_DIR$/../media/libraries/exoplayer_dash\" />\r\n            <option value=\"$PROJECT_DIR$/../media/libraries/exoplayer_hls\" />\r\n            <option value=\"$PROJECT_DIR$/../media/libraries/exoplayer_ima\" />\r\n            <option value=\"$PROJECT_DIR$/../media/libraries/exoplayer_rtsp\" />\r\n            <option value=\"$PROJECT_DIR$/../media/libraries/exoplayer_smoothstreaming\" />\r\n            <option value=\"$PROJECT_DIR$/../media/libraries/exoplayer_workmanager\" />\r\n            <option value=\"$PROJECT_DIR$/../media/libraries/extractor\" />\r\n            <option value=\"$PROJECT_DIR$/../media/libraries/muxer\" />\r\n            <option value=\"$PROJECT_DIR$/../media/libraries/session\" />\r\n            <option value=\"$PROJECT_DIR$/../media/libraries/test_data\" />\r\n            <option value=\"$PROJECT_DIR$/../media/libraries/test_utils\" />\r\n            <option value=\"$PROJECT_DIR$/../media/libraries/test_utils_robolectric\" />\r\n            <option value=\"$PROJECT_DIR$/../media/libraries/transformer\" />\r\n            <option value=\"$PROJECT_DIR$/../media/libraries/ui\" />\r\n            <option value=\"$PROJECT_DIR$/../media/libraries/ui_leanback\" />\r\n            <option value=\"$PROJECT_DIR$\" />\r\n            <option value=\"$PROJECT_DIR$/allinone\" />\r\n            <option value=\"$PROJECT_DIR$/core\" />\r\n            <option value=\"$PROJECT_DIR$/core/data\" />\r\n            <option value=\"$PROJECT_DIR$/core/designsystem\" />\r\n            <option value=\"$PROJECT_DIR$/core/util\" />\r\n            <option value=\"$PROJECT_DIR$/ijkplayer-java\" />\r\n            <option value=\"$PROJECT_DIR$/media3\" />\r\n            <option value=\"$PROJECT_DIR$/mobile\" />\r\n            <option value=\"$PROJECT_DIR$/tv\" />\r\n          </set>\r\n        </option>\r\n        <option name=\"resolveExternalAnnotations\" value=\"false\" />\r\n      </GradleProjectSettings>\r\n    </option>\r\n  </component>\r\n</project>
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/.idea/gradle.xml b/.idea/gradle.xml
--- a/.idea/gradle.xml	(revision ****************************************)
+++ b/.idea/gradle.xml	(date 1755499424369)
@@ -10,53 +10,16 @@
         <option name="modules">
           <set>
             <option value="$PROJECT_DIR$/../YYKM/tbsx5" />
-            <option value="$PROJECT_DIR$/../media/libraries/cast" />
-            <option value="$PROJECT_DIR$/../media/libraries/common" />
-            <option value="$PROJECT_DIR$/../media/libraries/common_ktx" />
-            <option value="$PROJECT_DIR$/../media/libraries/container" />
-            <option value="$PROJECT_DIR$/../media/libraries/database" />
-            <option value="$PROJECT_DIR$/../media/libraries/datasource" />
-            <option value="$PROJECT_DIR$/../media/libraries/datasource_cronet" />
-            <option value="$PROJECT_DIR$/../media/libraries/datasource_httpengine" />
-            <option value="$PROJECT_DIR$/../media/libraries/datasource_okhttp" />
-            <option value="$PROJECT_DIR$/../media/libraries/datasource_rtmp" />
-            <option value="$PROJECT_DIR$/../media/libraries/decoder" />
-            <option value="$PROJECT_DIR$/../media/libraries/decoder_av1" />
-            <option value="$PROJECT_DIR$/../media/libraries/decoder_ffmpeg" />
-            <option value="$PROJECT_DIR$/../media/libraries/decoder_flac" />
-            <option value="$PROJECT_DIR$/../media/libraries/decoder_iamf" />
-            <option value="$PROJECT_DIR$/../media/libraries/decoder_opus" />
-            <option value="$PROJECT_DIR$/../media/libraries/decoder_vp9" />
-            <option value="$PROJECT_DIR$/../media/libraries/effect" />
-            <option value="$PROJECT_DIR$/../media/libraries/exoplayer" />
-            <option value="$PROJECT_DIR$/../media/libraries/exoplayer_dash" />
-            <option value="$PROJECT_DIR$/../media/libraries/exoplayer_hls" />
-            <option value="$PROJECT_DIR$/../media/libraries/exoplayer_ima" />
-            <option value="$PROJECT_DIR$/../media/libraries/exoplayer_rtsp" />
-            <option value="$PROJECT_DIR$/../media/libraries/exoplayer_smoothstreaming" />
-            <option value="$PROJECT_DIR$/../media/libraries/exoplayer_workmanager" />
-            <option value="$PROJECT_DIR$/../media/libraries/extractor" />
-            <option value="$PROJECT_DIR$/../media/libraries/muxer" />
-            <option value="$PROJECT_DIR$/../media/libraries/session" />
-            <option value="$PROJECT_DIR$/../media/libraries/test_data" />
-            <option value="$PROJECT_DIR$/../media/libraries/test_utils" />
-            <option value="$PROJECT_DIR$/../media/libraries/test_utils_robolectric" />
-            <option value="$PROJECT_DIR$/../media/libraries/transformer" />
-            <option value="$PROJECT_DIR$/../media/libraries/ui" />
-            <option value="$PROJECT_DIR$/../media/libraries/ui_leanback" />
             <option value="$PROJECT_DIR$" />
-            <option value="$PROJECT_DIR$/allinone" />
             <option value="$PROJECT_DIR$/core" />
             <option value="$PROJECT_DIR$/core/data" />
             <option value="$PROJECT_DIR$/core/designsystem" />
             <option value="$PROJECT_DIR$/core/util" />
             <option value="$PROJECT_DIR$/ijkplayer-java" />
-            <option value="$PROJECT_DIR$/media3" />
             <option value="$PROJECT_DIR$/mobile" />
             <option value="$PROJECT_DIR$/tv" />
           </set>
         </option>
-        <option name="resolveExternalAnnotations" value="false" />
       </GradleProjectSettings>
     </option>
   </component>
Index: tv/src/main/java/top/yogiczy/mytv/tv/utlis/HttpServer.kt
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package top.yogiczy.mytv.tv.utlis\r\n\r\nimport android.content.Context\r\nimport android.content.Intent\r\nimport com.koushikdutta.async.AsyncServer\r\nimport com.koushikdutta.async.http.body.JSONObjectBody\r\nimport com.koushikdutta.async.http.body.MultipartFormDataBody\r\nimport com.koushikdutta.async.http.body.StringBody\r\nimport com.koushikdutta.async.http.server.AsyncHttpServer\r\nimport com.koushikdutta.async.http.server.AsyncHttpServerRequest\r\nimport com.koushikdutta.async.http.server.AsyncHttpServerResponse\r\nimport kotlinx.coroutines.CoroutineScope\r\nimport kotlinx.coroutines.Dispatchers\r\nimport kotlinx.coroutines.launch\r\nimport kotlinx.coroutines.runBlocking\r\nimport kotlinx.serialization.Serializable\r\nimport kotlinx.serialization.encodeToString\r\nimport top.yogiczy.mytv.core.data.entities.epgsource.EpgSource\r\nimport top.yogiczy.mytv.core.data.entities.epgsource.EpgSourceList\r\nimport top.yogiczy.mytv.core.data.entities.iptvsource.IptvSource\r\nimport top.yogiczy.mytv.core.data.entities.iptvsource.IptvSourceList\r\nimport top.yogiczy.mytv.core.data.repositories.epg.EpgRepository\r\nimport top.yogiczy.mytv.core.data.repositories.iptv.IptvRepository\r\nimport top.yogiczy.mytv.core.data.utils.ChannelAlias\r\nimport top.yogiczy.mytv.core.data.utils.Constants\r\nimport top.yogiczy.mytv.core.data.utils.Globals\r\nimport top.yogiczy.mytv.core.data.utils.Loggable\r\nimport top.yogiczy.mytv.core.data.utils.Logger\r\nimport top.yogiczy.mytv.core.util.utils.ApkInstaller\r\nimport top.yogiczy.mytv.tv.BuildConfig\r\nimport top.yogiczy.mytv.tv.HttpServerService\r\nimport top.yogiczy.mytv.tv.R\r\nimport top.yogiczy.mytv.tv.sync.CloudSync\r\nimport top.yogiczy.mytv.tv.sync.CloudSyncData\r\nimport top.yogiczy.mytv.tv.ui.material.Snackbar\r\nimport top.yogiczy.mytv.tv.ui.material.SnackbarType\r\nimport top.yogiczy.mytv.tv.ui.utils.Configs\r\nimport java.io.File\r\nimport java.net.Inet4Address\r\nimport java.net.NetworkInterface\r\nimport java.net.SocketException\r\n\r\n\r\nobject HttpServer : Loggable(\"HttpServer\") {\r\n    private const val SERVER_PORT = 10481\r\n    private val uploadedApkFile by lazy {\r\n        File(Globals.cacheDir, \"uploaded_apk.apk\").apply { deleteOnExit() }\r\n    }\r\n\r\n    val serverUrl by lazy { \"http://${getLocalIpAddress()}:${SERVER_PORT}\" }\r\n\r\n    fun start(context: Context) {\r\n        CoroutineScope(Dispatchers.IO).launch {\r\n            try {\r\n                val server = AsyncHttpServer()\r\n\r\n                server.addAction(\"OPTIONS\", \".+\") { _, response ->\r\n                    wrapResponse(response)\r\n                    response.send(\"ok\")\r\n                }\r\n\r\n                server.listen(AsyncServer.getDefault(), SERVER_PORT)\r\n\r\n                server.get(\"/\") { _, response ->\r\n                    handleRawResource(response, context, \"text/html\", R.raw.web_push)\r\n                }\r\n                server.get(\"/web_push_css.css\") { _, response ->\r\n                    handleRawResource(response, context, \"text/css\", R.raw.web_push_css)\r\n                }\r\n                server.get(\"/web_push_js.js\") { _, response ->\r\n                    handleRawResource(response, context, \"text/javascript\", R.raw.web_push_js)\r\n                }\r\n\r\n                server.get(\"/advance\") { _, response ->\r\n                    handleAssets(response, context, \"text/html\", \"remote-configs/index.html\")\r\n                }\r\n\r\n                server.get(\"/remote-configs/(.*)\") { request, response ->\r\n                    val contentType = when (request.path.split(\".\").last()) {\r\n                        \"css\" -> \"text/css\"\r\n                        \"js\" -> \"text/javascript\"\r\n                        \"html\" -> \"text/html\"\r\n                        \"json\" -> \"application/json\"\r\n                        \"svg\" -> \"image/svg+xml\"\r\n                        \"png\" -> \"image/png\"\r\n                        else -> \"text/plain\"\r\n                    }\r\n\r\n                    handleAssets(response, context, contentType, request.path.removePrefix(\"/\"))\r\n                }\r\n\r\n                server.get(\"/api/info\") { _, response ->\r\n                    handleGetInfo(response)\r\n                }\r\n\r\n                server.post(\"/api/iptv-source/push\") { request, response ->\r\n                    handleIptvSourcePush(request, response)\r\n                }\r\n\r\n                server.post(\"/api/epg-source/push\") { request, response ->\r\n                    handleEpgSourcePush(request, response)\r\n                }\r\n\r\n                server.get(\"/api/channel-alias\") { _, response ->\r\n                    handleGetChannelAlias(response)\r\n                }\r\n\r\n                server.post(\"/api/channel-alias\") { request, response ->\r\n                    handleUpdateChannelAlias(request, response)\r\n                }\r\n\r\n                server.get(\"/api/configs\") { _, response ->\r\n                    handleConfigsGet(response)\r\n                }\r\n\r\n                server.post(\"/api/configs\") { request, response ->\r\n                    handleConfigsPush(request, response)\r\n                }\r\n\r\n                server.post(\"/api/upload/apk\") { request, response ->\r\n                    handleUploadApk(request, response, context)\r\n                }\r\n\r\n                server.get(\"/api/cloud-sync/data\") { _, response ->\r\n                    handleCloudSyncDataGet(response)\r\n                }\r\n\r\n                server.post(\"/api/cloud-sync/data\") { request, response ->\r\n                    handleCloudSyncDataPost(request, response)\r\n                }\r\n\r\n                server.get(\"/api/about\") { _, response ->\r\n                    handleAboutGet(response)\r\n                }\r\n\r\n                server.get(\"/api/logs\") { _, response ->\r\n                    handleLogsGet(response)\r\n                }\r\n\r\n                server.get(\"/api/file/content\") { request, response ->\r\n                    handleFileContentGet(request, response)\r\n                }\r\n\r\n                server.post(\"/api/file/content\") { request, response ->\r\n                    handleFileContentPost(request, response)\r\n                }\r\n\r\n                server.post(\"/api/file/content-with-dir\") { request, response ->\r\n                    handleFileContentWithDirPost(request, response)\r\n                }\r\n\r\n                server.post(\"/api/upload/allinone\") { request, response ->\r\n                    handleUploadAllInOne(request, response)\r\n                }\r\n\r\n                log.i(\"设置服务已启动: $serverUrl\")\r\n            } catch (ex: Exception) {\r\n                log.e(\"设置服务启动失败: ${ex.message}\", ex)\r\n                launch(Dispatchers.Main) {\r\n                    Snackbar.show(\"设置服务启动失败\", type = SnackbarType.ERROR)\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    private fun wrapResponse(response: AsyncHttpServerResponse) = response.apply {\r\n        headers.set(\"Access-Control-Allow-Methods\", \"POST, GET, DELETE, PUT, OPTIONS\")\r\n        headers.set(\"Access-Control-Allow-Origin\", \"*\")\r\n        headers.set(\"Access-Control-Allow-Headers\", \"Origin, Content-Type, X-Auth-Token\")\r\n    }\r\n\r\n    private fun responseSuccess(response: AsyncHttpServerResponse) {\r\n        wrapResponse(response).apply {\r\n            setContentType(\"application/json\")\r\n            send(\"{\\\"code\\\": 0}\")\r\n        }\r\n    }\r\n\r\n    private fun handleRawResource(\r\n        response: AsyncHttpServerResponse,\r\n        context: Context,\r\n        contentType: String,\r\n        id: Int,\r\n    ) {\r\n        wrapResponse(response).apply {\r\n            setContentType(contentType)\r\n            send(context.resources.openRawResource(id).readBytes().decodeToString())\r\n        }\r\n    }\r\n\r\n    private fun handleAssets(\r\n        response: AsyncHttpServerResponse,\r\n        context: Context,\r\n        contentType: String,\r\n        filename: String,\r\n    ) {\r\n        wrapResponse(response).apply {\r\n            setContentType(contentType)\r\n            send(context.assets.open(filename).reader().readText())\r\n        }\r\n    }\r\n\r\n    private fun handleGetInfo(response: AsyncHttpServerResponse) {\r\n        wrapResponse(response).apply {\r\n            setContentType(\"application/json\")\r\n            send(\r\n                Globals.json.encodeToString(\r\n                    AppInfo(\r\n                        appTitle = Constants.APP_TITLE,\r\n                        appRepo = Constants.APP_REPO,\r\n                        logHistory = Logger.history,\r\n                    )\r\n                )\r\n            )\r\n        }\r\n    }\r\n\r\n    private fun handleIptvSourcePush(\r\n        request: AsyncHttpServerRequest,\r\n        response: AsyncHttpServerResponse,\r\n    ) {\r\n        val body = request.getBody<JSONObjectBody>().get()\r\n        val name = body.get(\"name\").toString()\r\n        val type = body.get(\"type\").toString()\r\n        val url = body.get(\"url\").toString()\r\n        val filePath = body.get(\"filePath\").toString()\r\n        val content = body.get(\"content\").toString()\r\n\r\n        var newIptvSource: IptvSource? = null\r\n\r\n        when (type) {\r\n            \"url\" -> {\r\n                newIptvSource = IptvSource(name, url)\r\n            }\r\n\r\n            \"file\" -> {\r\n                newIptvSource = IptvSource(name, filePath, true)\r\n            }\r\n\r\n            \"content\" -> {\r\n                val file =\r\n                    File(Globals.fileDir, \"iptv_source_local_${System.currentTimeMillis()}.txt\")\r\n                file.writeText(content)\r\n                newIptvSource = IptvSource(name, file.path, true)\r\n            }\r\n        }\r\n\r\n        newIptvSource?.let {\r\n            Configs.iptvSourceList = IptvSourceList(Configs.iptvSourceList + it)\r\n            Configs.iptvSourceCurrent = it\r\n        }\r\n\r\n        responseSuccess(response)\r\n    }\r\n\r\n    private fun handleEpgSourcePush(\r\n        request: AsyncHttpServerRequest,\r\n        response: AsyncHttpServerResponse,\r\n    ) {\r\n        val body = request.getBody<JSONObjectBody>().get()\r\n        val name = body.get(\"name\").toString()\r\n        val url = body.get(\"url\").toString()\r\n\r\n        EpgSource(name, url).let {\r\n            Configs.epgSourceList = EpgSourceList(Configs.epgSourceList + it)\r\n            Configs.epgSourceCurrent = it\r\n        }\r\n\r\n        responseSuccess(response)\r\n    }\r\n\r\n    private fun handleGetChannelAlias(response: AsyncHttpServerResponse) {\r\n        wrapResponse(response).apply {\r\n            send(runCatching { ChannelAlias.aliasFile.readText() }.getOrElse { \"\" })\r\n        }\r\n    }\r\n\r\n    private fun handleUpdateChannelAlias(\r\n        request: AsyncHttpServerRequest,\r\n        response: AsyncHttpServerResponse,\r\n    ) {\r\n        val alias = request.getBody<StringBody>().get()\r\n\r\n        ChannelAlias.aliasFile.writeText(alias)\r\n        runBlocking {\r\n            IptvRepository.clearAllCache()\r\n            EpgRepository.clearAllCache()\r\n        }\r\n\r\n        responseSuccess(response)\r\n    }\r\n\r\n    private fun handleConfigsGet(response: AsyncHttpServerResponse) {\r\n        wrapResponse(response).apply {\r\n            setContentType(\"application/json\")\r\n            send(Globals.json.encodeToString(Configs.toPartial()))\r\n        }\r\n    }\r\n\r\n    private fun handleConfigsPush(\r\n        request: AsyncHttpServerRequest,\r\n        response: AsyncHttpServerResponse,\r\n    ) {\r\n        val body = request.getBody<JSONObjectBody>().get()\r\n        val configs = Globals.json.decodeFromString<Configs.Partial>(body.toString())\r\n        Configs.fromPartial(configs)\r\n\r\n        responseSuccess(response)\r\n    }\r\n\r\n    private fun handleUploadApk(\r\n        request: AsyncHttpServerRequest,\r\n        response: AsyncHttpServerResponse,\r\n        context: Context,\r\n    ) {\r\n        val body = request.getBody<MultipartFormDataBody>()\r\n\r\n        val os = uploadedApkFile.outputStream()\r\n        val contentLength = request.headers[\"Content-Length\"]?.toLong() ?: 1\r\n        var hasReceived = 0L\r\n\r\n        body.setMultipartCallback { part ->\r\n            if (part.isFile) {\r\n                body.setDataCallback { _, bb ->\r\n                    val byteArray = bb.allByteArray\r\n                    hasReceived += byteArray.size\r\n                    Snackbar.show(\r\n                        \"正在接收文件: ${(hasReceived * 100f / contentLength).toInt()}%\",\r\n                        leadingLoading = true,\r\n                        id = \"uploading_apk\",\r\n                    )\r\n                    os.write(byteArray)\r\n                }\r\n            }\r\n        }\r\n\r\n        body.setEndCallback {\r\n            Snackbar.show(\"正在准备安装，请稍后...\", leadingLoading = true, duration = 10_000)\r\n            body.dataEmitter.close()\r\n            os.flush()\r\n            os.close()\r\n            ApkInstaller.installApk(context, uploadedApkFile.path)\r\n        }\r\n\r\n        responseSuccess(response)\r\n    }\r\n\r\n    private fun handleUploadAllInOne(\r\n        request: AsyncHttpServerRequest,\r\n        response: AsyncHttpServerResponse,\r\n    ) {\r\n        val body = request.getBody<MultipartFormDataBody>()\r\n\r\n        val contentLength = request.headers[\"Content-Length\"]?.toLong() ?: 1\r\n        var hasReceived = 0L\r\n\r\n        val allinoneFile = File(Globals.fileDir, \"uploads/allinone\").apply { parentFile?.mkdirs() }\r\n\r\n        body.setMultipartCallback { part ->\r\n            if (part.isFile) {\r\n                with(allinoneFile.outputStream()) {\r\n\r\n                    body.setDataCallback { _, bb ->\r\n                        val byteArray = bb.allByteArray\r\n                        hasReceived += byteArray.size\r\n                        Snackbar.show(\r\n                            \"正在接收文件: ${(hasReceived * 100f / contentLength).toInt()}%\",\r\n                            leadingLoading = true,\r\n                            id = \"uploading_file\",\r\n                        )\r\n                        write(byteArray)\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        body.setEndCallback {\r\n            body.dataEmitter.close()\r\n            Configs.feiyangAllInOneFilePath = allinoneFile.absolutePath\r\n            Snackbar.show(\"文件接收完成\", id = \"uploading_file\")\r\n        }\r\n\r\n        responseSuccess(response)\r\n    }\r\n\r\n    private fun handleCloudSyncDataGet(response: AsyncHttpServerResponse) {\r\n        wrapResponse(response).apply {\r\n            setContentType(\"application/json\")\r\n            send(Globals.json.encodeToString(runBlocking { CloudSync.getData() }))\r\n        }\r\n    }\r\n\r\n    private fun handleCloudSyncDataPost(\r\n        request: AsyncHttpServerRequest,\r\n        response: AsyncHttpServerResponse\r\n    ) {\r\n        val body = request.getBody<JSONObjectBody>().get()\r\n        val data = Globals.json.decodeFromString<CloudSyncData>(body.toString())\r\n        runBlocking { data.apply() }\r\n\r\n        responseSuccess(response)\r\n    }\r\n\r\n    private fun handleAboutGet(response: AsyncHttpServerResponse) {\r\n        wrapResponse(response).apply {\r\n            setContentType(\"application/json\")\r\n            send(\r\n                Globals.json.encodeToString(\r\n                    AppAbout(\r\n                        applicationId = BuildConfig.APPLICATION_ID,\r\n                        flavor = BuildConfig.FLAVOR,\r\n                        buildType = BuildConfig.BUILD_TYPE,\r\n                        versionCode = BuildConfig.VERSION_CODE,\r\n                        versionName = BuildConfig.VERSION_NAME,\r\n                        deviceName = Globals.deviceName,\r\n                        deviceId = Globals.deviceId,\r\n                    )\r\n                )\r\n            )\r\n        }\r\n    }\r\n\r\n    private fun handleLogsGet(response: AsyncHttpServerResponse) {\r\n        wrapResponse(response).apply {\r\n            setContentType(\"application/json\")\r\n            send(Globals.json.encodeToString(Logger.history))\r\n        }\r\n    }\r\n\r\n    private fun handleFileContentGet(\r\n        request: AsyncHttpServerRequest,\r\n        response: AsyncHttpServerResponse\r\n    ) {\r\n        val path = request.query.getString(\"path\")\r\n\r\n        val file = File(path).takeIf { it.exists() } ?: run {\r\n            return response.code(404).send(\"File not found\")\r\n        }\r\n\r\n        wrapResponse(response).send(file.readText())\r\n    }\r\n\r\n    private fun handleFileContentPost(\r\n        request: AsyncHttpServerRequest,\r\n        response: AsyncHttpServerResponse\r\n    ) {\r\n        val body = request.getBody<JSONObjectBody>().get()\r\n        val path = body.get(\"path\").toString()\r\n        val content = body.get(\"content\").toString()\r\n\r\n        val file = File(path)\r\n\r\n        if (file.exists()) {\r\n            file.writeText(content)\r\n        } else {\r\n            file.parentFile?.mkdirs()\r\n            file.writeText(content)\r\n        }\r\n\r\n        responseSuccess(response)\r\n    }\r\n\r\n    private fun handleFileContentWithDirPost(\r\n        request: AsyncHttpServerRequest,\r\n        response: AsyncHttpServerResponse\r\n    ) {\r\n        val body = request.getBody<JSONObjectBody>().get()\r\n        val dir = body.get(\"dir\").toString()\r\n        val filename = body.get(\"filename\").toString()\r\n        val content = body.get(\"content\").toString()\r\n\r\n        val file = when (dir) {\r\n            \"cache\" -> File(Globals.cacheDir, filename)\r\n            \"file\" -> File(Globals.fileDir, filename)\r\n            else -> return response.code(400).send(\"Invalid dir\")\r\n        }\r\n\r\n        if (file.exists()) {\r\n            file.writeText(content)\r\n        } else {\r\n            file.parentFile?.mkdirs()\r\n            file.writeText(content)\r\n        }\r\n\r\n        wrapResponse(response).send(file.path)\r\n    }\r\n\r\n    private fun getLocalIpAddress(): String {\r\n        val defaultIp = \"127.0.0.1\"\r\n\r\n        try {\r\n            val en = NetworkInterface.getNetworkInterfaces()\r\n            while (en.hasMoreElements()) {\r\n                val intf = en.nextElement()\r\n                val enumIpAddr = intf.inetAddresses\r\n                while (enumIpAddr.hasMoreElements()) {\r\n                    val inetAddress = enumIpAddr.nextElement()\r\n                    if (!inetAddress.isLoopbackAddress && inetAddress is Inet4Address) {\r\n                        val ip = inetAddress.hostAddress ?: defaultIp\r\n                        val isValid = when {\r\n                            ip.startsWith(\"192.168.\") -> true\r\n                            ip.startsWith(\"10.\") -> true\r\n                            ip.startsWith(\"172.\") -> {\r\n                                val parts = ip.split(\".\")\r\n                                val secondOctet = parts.getOrNull(1)?.toIntOrNull()\r\n                                secondOctet in 16..31\r\n                            }\r\n\r\n                            else -> false\r\n                        }\r\n\r\n                        if (isValid)\r\n                            return ip\r\n                    }\r\n                }\r\n            }\r\n            return defaultIp\r\n        } catch (ex: SocketException) {\r\n            log.e(\"IP Address: ${ex.message}\", ex)\r\n            return defaultIp\r\n        }\r\n    }\r\n\r\n    fun startService(context: Context) {\r\n        runCatching {\r\n            context.startService(Intent(context, HttpServerService::class.java))\r\n        }.onFailure { it.printStackTrace() }\r\n    }\r\n\r\n    fun stopService(context: Context) {\r\n        runCatching {\r\n            context.stopService(Intent(context, HttpServerService::class.java))\r\n        }.onFailure { it.printStackTrace() }\r\n    }\r\n}\r\n\r\n@Serializable\r\nprivate data class AppInfo(\r\n    val appTitle: String,\r\n    val appRepo: String,\r\n    val logHistory: List<Logger.HistoryItem>,\r\n)\r\n\r\n@Serializable\r\nprivate data class AppAbout(\r\n    val applicationId: String,\r\n    val flavor: String,\r\n    val buildType: String,\r\n    val versionCode: Int,\r\n    val versionName: String,\r\n    val deviceName: String,\r\n    val deviceId: String,\r\n)
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/tv/src/main/java/top/yogiczy/mytv/tv/utlis/HttpServer.kt b/tv/src/main/java/top/yogiczy/mytv/tv/utlis/HttpServer.kt
--- a/tv/src/main/java/top/yogiczy/mytv/tv/utlis/HttpServer.kt	(revision ****************************************)
+++ b/tv/src/main/java/top/yogiczy/mytv/tv/utlis/HttpServer.kt	(date 1755499320812)
@@ -149,10 +149,6 @@
                     handleFileContentWithDirPost(request, response)
                 }
 
-                server.post("/api/upload/allinone") { request, response ->
-                    handleUploadAllInOne(request, response)
-                }
-
                 log.i("设置服务已启动: $serverUrl")
             } catch (ex: Exception) {
                 log.e("设置服务启动失败: ${ex.message}", ex)
@@ -342,44 +338,6 @@
             ApkInstaller.installApk(context, uploadedApkFile.path)
         }
 
-        responseSuccess(response)
-    }
-
-    private fun handleUploadAllInOne(
-        request: AsyncHttpServerRequest,
-        response: AsyncHttpServerResponse,
-    ) {
-        val body = request.getBody<MultipartFormDataBody>()
-
-        val contentLength = request.headers["Content-Length"]?.toLong() ?: 1
-        var hasReceived = 0L
-
-        val allinoneFile = File(Globals.fileDir, "uploads/allinone").apply { parentFile?.mkdirs() }
-
-        body.setMultipartCallback { part ->
-            if (part.isFile) {
-                with(allinoneFile.outputStream()) {
-
-                    body.setDataCallback { _, bb ->
-                        val byteArray = bb.allByteArray
-                        hasReceived += byteArray.size
-                        Snackbar.show(
-                            "正在接收文件: ${(hasReceived * 100f / contentLength).toInt()}%",
-                            leadingLoading = true,
-                            id = "uploading_file",
-                        )
-                        write(byteArray)
-                    }
-                }
-            }
-        }
-
-        body.setEndCallback {
-            body.dataEmitter.close()
-            Configs.feiyangAllInOneFilePath = allinoneFile.absolutePath
-            Snackbar.show("文件接收完成", id = "uploading_file")
-        }
-
         responseSuccess(response)
     }
 
diff --git a/allinone/consumer-rules.pro b/allinone/consumer-rules.pro
deleted file mode 100644
