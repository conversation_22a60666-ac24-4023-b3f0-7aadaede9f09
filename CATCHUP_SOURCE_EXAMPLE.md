# Catchup-Source 回放功能实现说明

## 功能概述

本次修改为项目添加了对M3U8文件中`catchup-source`字段的支持，允许用户配置带时间参数的回放源。

## 修改的文件

### 1. 核心数据结构修改

#### `ChannelLine.kt`
- 添加了 `catchupSource: String?` 字段用于存储回放源URL模板

#### `IptvParser.kt` 
- 在 `ChannelItem` 数据类中添加了 `catchupSource: String?` 字段
- 更新了转换逻辑以传递catchup-source信息

#### `M3uIptvParser.kt`
- 添加了对 `catchup-source="..."` 字段的解析支持
- 使用正则表达式提取catchup-source值

### 2. 回放逻辑修改

#### `ChannelUtil.kt`
- 添加了 `channelLineSupportPlayback()` 方法，支持检测catchup-source
- 添加了 `buildCatchupUrl()` 方法，用于构建带时间参数的回放URL
- 支持 `{utc:YmdHMS}` 和 `{utcend:YmdHMS}` 时间占位符替换

#### `MainContentState.kt`
- 修改了 `supportPlayback()` 方法使用新的检测逻辑
- 修改了 `changeCurrentChannel()` 方法，优先使用catchup-source构建回放URL

#### `ChannelLineItem.kt`
- 更新了回放标签显示逻辑，支持catchup-source的频道

## 使用示例

### M3U8配置示例

```m3u8
#EXTM3U
#EXTINF:-1 tvg-id="1" tvg-logo="https://example.com/logo.png" group-title="央视频道" catchup="default" catchup-source="rtsp://***************:1554/iptv/Tvod/iptv/001/001/ch12122514263996485740.rsc?tvdr={utc:YmdHMS}GMT-{utcend:YmdHMS}GMT",CCTV1
http://example.com/live/cctv1.m3u8

#EXTINF:-1 tvg-id="2" tvg-logo="https://example.com/logo2.png" group-title="央视频道" catchup-source="http://playback.server.com/stream?start={utc:YmdHMS}&end={utcend:YmdHMS}",CCTV2
http://example.com/live/cctv2.m3u8
```

### 回放URL生成示例

当用户选择回放2024年12月18日14:30:00到15:30:00的节目时：

**原始catchup-source:**
```
rtsp://***************:1554/iptv/Tvod/iptv/001/001/ch12122514263996485740.rsc?tvdr={utc:YmdHMS}GMT-{utcend:YmdHMS}GMT
```

**生成的回放URL:**
```
rtsp://***************:1554/iptv/Tvod/iptv/001/001/ch12122514263996485740.rsc?tvdr=20241218143000GMT-20241218153000GMT
```

## 支持的时间格式

- `{utc:YmdHMS}` - 开始时间，格式：yyyyMMddHHmmss（UTC时区）
- `{utcend:YmdHMS}` - 结束时间，格式：yyyyMMddHHmmss（UTC时区）

**重要说明：** 时间参数严格按照UTC时区进行格式化，确保与服务器端时间标准一致。

## 兼容性

- 保持与现有`pltv`/`tvod`回放方式的兼容性
- 优先使用catchup-source，如果没有则回退到传统方式
- 不影响现有的直播功能

## 测试

项目包含了完整的单元测试：
- `ChannelUtilTest.kt` - 测试回放URL构建和支持检测
- `M3uIptvParserTest.kt` - 测试M3U8解析功能

## 使用方法

1. 在M3U8文件中添加`catchup-source`字段
2. 确保catchup-source URL包含正确的时间占位符
3. 在EPG节目单中选择已结束的节目
4. 点击确认即可开始回放
