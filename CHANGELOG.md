# 更新日志

## [3.3.9] - 2025-01-16

### 优化

- 加载界面添加提示
- 优化m3u注释处理
- 混合模式添加部分地方台
- 优化webview直播源播放，所有以webview://开头的链接
- 忽略ssl错误

## [3.3.8] - 2025-01-02

### 优化

- 删除一些默认直播源、节目单
- 二进制携带参数，添加在路径后面；例：/path/to/binary -param1=value1 -p2

## [3.3.7] - 2024-11-30

### 新增

- 部分列表内循环

### 优化

- 解码器初始化失败后自动切换软解
- 优化aio启动

### 修复

- 可能修复画面问题

## [3.3.6] - 2024-11-23

### 新增

- 强制音频软解

### 优化

- 云同步忽略部分参数
- 还原部分节目单ui
- 优化本机ip获取
- 跟换ijk来源，移除av3a

### 修复

- 修复焦点部分在场景下崩溃

## 【3.3.5】 - 2024-11-05

### 新增

- 外部调用肥羊AllInOne
- 直播源、节目单快捷操作栏
- 云同步支持本地文件、webdav
- 直接进入直播设置，设置/应用

### 优化

- 优化多屏同播在非16:9显示下布局
- 多屏同播默认静音，聚焦状态下取消静音
- 优化频道信息过长导致UI错乱

### 修复

- 经典选台界面节目单问题
- 修复可能出现的的崩溃

## [3.3.3] - 2024-10-30

### 新增

- media3扩展av3a
- 新增应用崩溃拦截界面
- 支持m3u订阅源多行地址（聚合模式）
- 订阅源自定义转换js
- 首页订阅源长按（清除缓存并重启）
- 支持mpd DRM（仅media3）
- 支持视轨、音轨、字幕切换（仅media3）

### 优化

- 模糊匹配epg名称
- 优化epg解析逻辑，采用stream流，减少内存占用
- 重构频道收藏逻辑，切换订阅源后依旧保留
- 优化多线路记忆
- 优化应用模块初始化顺序
- 收藏界面根据订阅源分组
- 时间显示背景透明
- 推送订阅源后应用到当前
- 支持直播源$备注，播放时自动移除
- 完善配置页面
- 优化选台界面样式，更加紧凑

### 修复

- 修复应用关闭后，设置服务未及时关闭
- 修复ijkplayer起播卡顿
- 修复不同flavor云端数据恢复错误
- 修复fanmingming cctv2无法播放
- 修复节目单/跟随直播源失效
- 修复取消收藏时闪退

## [3.3.0] - 2024-10-10

### 新增

- m3u直播源支持多分组，例如：group-title="group1;group2"
- 新增换台列表首尾循环（设置/控制）
- 新增ijkPlayer播放器内核，支持av3a
- 新增播放器渲染方式（SurfaceView、TextureView）

### 优化

- 推送服务后台运行
- 点击二维码网页跳转
- 切换更新通道时立即检查更新
- 优化m3u直播源 x-tvg-url解析
- 优化txt直播源对注释的处理
- 优化分辨率tag名称
- 优化频道图标覆盖逻辑，覆盖图片若无法显示切换至原图标
- 优化播放器详细信息
- 优化相似频道合并、直播源混合速度

### 修复

- 修复预约UI未刷新
- 修复部分场景下闪退
- 缓解ANR问题

## [3.2.0] - 2024-09-29

### 新增

- 新增云同步（支持github gist、gitee gist、网络链接）
- 支持xmltv icon字段、多个display-name标记频道别名
- 支持节目单跟随直播源
- 支持频道别名通用后缀
- 支持画中画（设置/应用）

### 优化

- 添加播放器错误中文提示
- 优化触摸设备点击
- 优化相似频道合并时多线路名称
- 优化部分直播界面

### 修复

- 修复鸿蒙超级桌面未全屏
- 修复部分场景下节目单未刷新

## [3.1.0] - 2024-09-13

### 新增

- 新增多屏同播（Beta）
- 新增频道别名、相似频道合并
- 新增应用主题：copy from qq超级调色盘
- 新增频道图标强制覆盖
- 支持m3u文件中的 http-user-agent 参数，支持针对单个频道设置UA

### 优化

- 优化触摸设备焦点

### 修复

- 修复部分场景下闪退

## [3.0.0] - 2024-09-01

- 重构
