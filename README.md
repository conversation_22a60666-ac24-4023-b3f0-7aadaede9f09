<div align="center">
    <h1>天光云影</h1>
<div align="center">

![GitHub Repo stars](https://img.shields.io/github/stars/yaoxieyoulei/mytv-android)
![GitHub all releases](https://img.shields.io/github/downloads/yaoxieyoulei/mytv-android/total)
[![Android Sdk Require](https://img.shields.io/badge/Android-5.0%2B-informational?logo=android)](https://apilevels.com/#:~:text=Jetpack%20Compose%20requires%20a%20minSdk%20of%2021%20or%20higher)
[![GitHub](https://img.shields.io/github/license/yaoxieyoulei/mytv-android)](https://github.com/yaoxieyoulei/mytv-android)

</div>
    <p>使用Android原生开发的视频播放软件</p>
</div>

## 使用

### 操作方式

> 遥控器操作方式与主流视频播放软件类似； 

- 频道切换：使用上下方向键，或者数字键切换频道；屏幕上下滑动；
- 频道选择：OK键；单击屏幕；
- 线路切换：使用左右方向键；屏幕左右滑动；
- 设置页面：按下菜单、帮助键，长按OK键；双击、长按屏幕；

### 触摸键位对应

- 方向键：屏幕上下左右滑动
- OK键：点击屏幕
- 长按OK键：长按屏幕
- 菜单、帮助键：双击屏幕

### 自定义设置

- 访问以下网址：`http://<设备IP>:10481`

## 下载

可以通过右侧release进行下载或拉取代码到本地进行编译

## 说明

- 仅支持Android5及以上
- 网络环境必须支持IPV6（默认订阅源）
- 只在自家电视上测过，其他电视稳定性未知

## 更新日志

[更新日志](./CHANGELOG.md)

## 声明

此项目（天光云影）是个人为了兴趣而开发, 仅用于学习和测试。 所用API皆从官方网站收集, 不提供任何破解内容。

## 技术交流

Telegram: https://t.me/mytv_android

## 赞赏

<img src="./screenshots/mm_reward_qrcode.png" width="48%"/>

## 致谢

- [my-tv](https://github.com/lizongying/my-tv)
- [参考设计稿](https://github.com/lizongying/my-tv/issues/594)
- [live](https://github.com/fanmingming/live)
- 等等
