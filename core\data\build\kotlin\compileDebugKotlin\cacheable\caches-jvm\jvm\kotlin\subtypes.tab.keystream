2kotlinx.serialization.internal.GeneratedSerializerkotlin.collections.Listjava.lang.Exception)top.yogiczy.mytv.core.data.utils.Loggable;top.yogiczy.mytv.core.data.repositories.FileCacheRepository>top.yogiczy.mytv.core.data.repositories.epg.fetcher.EpgFetcherCtop.yogiczy.mytv.core.data.repositories.git.parser.GitReleaseParser>top.yogiczy.mytv.core.data.repositories.iptv.parser.IptvParserkotlin.Enumandroidx.collection.LruCache                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                