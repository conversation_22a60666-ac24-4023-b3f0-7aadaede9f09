package top.yogiczy.mytv.core.data.repositories.iptv.parser

import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.Assert.*

class M3uIptvParserTest {

    private val parser = M3uIptvParser()

    @Test
    fun testParseCatchupSource() = runBlocking {
        val m3uContent = """
            #EXTM3U
            #EXTINF:-1 tvg-id="1" tvg-logo="https://gcore.jsdelivr.net/gh/taksssss/tv/icon/CCTV1.png" group-title="央视频道" catchup="default" catchup-source="rtsp://***************:1554/iptv/Tvod/iptv/001/001/ch12122514263996485740.rsc?tvdr={utc:YmdHMS}GMT-{utcend:YmdHMS}GMT",CCTV1
            http://example.com/live/cctv1.m3u8
            #EXTINF:-1 tvg-id="2" tvg-logo="https://gcore.jsdelivr.net/gh/taksssss/tv/icon/CCTV2.png" group-title="央视频道",CCTV2
            http://example.com/live/cctv2.m3u8
        """.trimIndent()

        val result = parser.parse(m3uContent)

        assertEquals("Should parse 2 channels", 2, result.size)

        val cctv1 = result.find { it.name == "CCTV1" }
        assertNotNull("CCTV1 should be found", cctv1)
        assertEquals("CCTV1 should have catchup-source", 
                     "rtsp://***************:1554/iptv/Tvod/iptv/001/001/ch12122514263996485740.rsc?tvdr={utc:YmdHMS}GMT-{utcend:YmdHMS}GMT", 
                     cctv1?.catchupSource)

        val cctv2 = result.find { it.name == "CCTV2" }
        assertNotNull("CCTV2 should be found", cctv2)
        assertNull("CCTV2 should not have catchup-source", cctv2?.catchupSource)
    }

    @Test
    fun testParseWithoutCatchupSource() = runBlocking {
        val m3uContent = """
            #EXTM3U
            #EXTINF:-1 tvg-id="1" tvg-logo="https://example.com/logo.png" group-title="测试频道",测试频道1
            http://example.com/live/test1.m3u8
        """.trimIndent()

        val result = parser.parse(m3uContent)

        assertEquals("Should parse 1 channel", 1, result.size)
        val channel = result.first()
        assertEquals("Channel name should be correct", "测试频道1", channel.name)
        assertNull("Channel should not have catchup-source", channel.catchupSource)
    }

    @Test
    fun testParseMultipleChannelsWithMixedCatchupSource() = runBlocking {
        val m3uContent = """
            #EXTM3U
            #EXTINF:-1 tvg-id="1" group-title="央视频道" catchup-source="http://example.com/playback?start={utc:YmdHMS}&end={utcend:YmdHMS}",CCTV1
            http://example.com/live/cctv1.m3u8
            #EXTINF:-1 tvg-id="2" group-title="央视频道",CCTV2
            http://example.com/live/cctv2.m3u8
            #EXTINF:-1 tvg-id="3" group-title="卫视频道" catchup-source="rtsp://server.com/playback/{utc:YmdHMS}-{utcend:YmdHMS}",湖南卫视
            http://example.com/live/hunan.m3u8
        """.trimIndent()

        val result = parser.parse(m3uContent)

        assertEquals("Should parse 3 channels", 3, result.size)

        val cctv1 = result.find { it.name == "CCTV1" }
        assertEquals("CCTV1 catchup-source should be correct", 
                     "http://example.com/playback?start={utc:YmdHMS}&end={utcend:YmdHMS}", 
                     cctv1?.catchupSource)

        val cctv2 = result.find { it.name == "CCTV2" }
        assertNull("CCTV2 should not have catchup-source", cctv2?.catchupSource)

        val hunan = result.find { it.name == "湖南卫视" }
        assertEquals("湖南卫视 catchup-source should be correct", 
                     "rtsp://server.com/playback/{utc:YmdHMS}-{utcend:YmdHMS}", 
                     hunan?.catchupSource)
    }

    @Test
    fun testIsSupport() {
        assertTrue("Should support M3U format", parser.isSupport("", "#EXTM3U\n#EXTINF:-1,Test\nhttp://example.com"))
        assertFalse("Should not support non-M3U format", parser.isSupport("", "Test,http://example.com"))
    }
}
