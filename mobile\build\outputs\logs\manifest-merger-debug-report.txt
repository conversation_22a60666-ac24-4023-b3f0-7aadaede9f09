-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml:2:1-23:12
INJECTED from C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml:2:1-23:12
INJECTED from C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml:2:1-23:12
INJECTED from C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml:2:1-23:12
MERGED from [:core:data] C:\Users\<USER>\StudioProjects\mytv-android\core\data\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:core:designsystem] C:\Users\<USER>\StudioProjects\mytv-android\core\designsystem\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:core:util] C:\Users\<USER>\StudioProjects\mytv-android\core\util\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\018c63b1485334add67611329c38a7ad\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c98e1a41ddbd0f58bbf4b7505ad9616c\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.8.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\e27b26ab4fdbef454305ada43ffa6aaf\transformed\navigation-common-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.8.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\00b00a57a1321b3abddaddf7bed980a7\transformed\navigation-runtime-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\901134bfa3027fdc4efc5e9b1dbbf70b\transformed\navigation-common-ktx-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\a78bc4bbe8c8b7fc76a9ffad5cc4261e\transformed\navigation-runtime-ktx-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.8.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\eb7529dec08a16f0ba21b419b9e507fa\transformed\navigation-compose-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\136feb759fbdaa722f3905caf7f29ff7\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\30e8e953c8ca6396d05eae233b004a84\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\790bca944b41d25834ca5cde0d5c6974\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\fd2de9df1fcb7d47d0f664953b17c729\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\a99e45a91607e1c71c206aab1452b5b3\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\b73b969236973d78657ede4f0f194d2a\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\b8b8e32440ebe6b1a27aee4fb611b8ab\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\2510da7f39df9715e457e0526689281c\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\db4a6c88d4347ce178fe3684539ea8e0\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\ab891530b64eecdf0e7491d248519c0a\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d8fa77734273d7df92383485cc746c5\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\7f33fb5b142e37b2e11aba2dc17a6b8a\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\fde23f3bbc10ce5c49e062b340e080e0\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\2beb69d6875208f2d04111a35001bba9\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\e3284905063e776d62ae70595f5b34b4\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a112f10a8620d44c07292457d5e069f0\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f7cfde7d9ac015d4e9e9435d414711\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8d6da967e4281651358e6dd3dac6ee93\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a24962cf41916db9572a74a797a0e06\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\30ac6839eb42bcb86357a0d144ffee2c\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\06f54bc868185307d2008338de4b6f8a\transformed\lifecycle-livedata-core-ktx-2.8.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\5f73895031d02ca8d1d64315fa193d91\transformed\lifecycle-livedata-2.8.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\a6c0d6aba12adda640fe295311b9cada\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d35c56726b65064adaa3dd1f91e7d98\transformed\lifecycle-livedata-core-2.8.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\d3e7b3daac682e3549d3763424bb713a\transformed\lifecycle-viewmodel-savedstate-2.8.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\63ccdacec2c798c3da3c085cba28b3e6\transformed\lifecycle-viewmodel-2.8.6\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\bdcbc52a7f4fc574f82c6a94b68ef448\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\c7360db8db21da0cd2a2513de2c76e03\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\afa94a096a9eeb83f65f48490bf80e41\transformed\lifecycle-viewmodel-ktx-2.8.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\4ac6608a66c236e16140b2df2b55713a\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\bba6757b190126e7429206a459baebc4\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\0dbb727f01cc86fe24c49a72666e9e7f\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\4fe448b3d7d63514c1e70b7a8388f22f\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\f0ce41bc1ab5a7ccfdd795c249b2c5b2\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\0bdb13050b39e5ec5f87e73f51b7ba0d\transformed\ui-test-manifest-1.7.4\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\1020d7831843ef581ff10fd389e83ec5\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\7e7e1990e3a2927aa964ef667f1a3836\transformed\activity-compose-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\ae30512adfd89f17d52d28ae78730ad7\transformed\activity-ktx-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\4c124e34a7f4172b432f9fe272bec823\transformed\activity-1.9.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e1e116a809e646e532a718de75b1ceef\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5bc5956aa20aa1b6e506265a26f6d087\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f73974b6797712fe76a28e8dd126afcc\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f40c40fb42cdb0ba22401bf8fc1df14\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f1fecd21f0cb9107e9c3751c3fdde6b\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8341b782ba39e57543abacf09495b30f\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f1e35c9300b09aae36d857ae78ea9a9a\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e8e26f56d8e20e616f6e0fb0878dba9\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\db0874ac1ef75b4773055a57b3321661\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\876bb1e606565bc27f304f01793afc9b\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\4e9d2cb82d2fe484f7dcd235e8334edb\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\aaf0057ac6b1dde88a2094d678e675b3\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8021988f1600a50d6929200a80486d92\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4545091883f438d426e29f97df13794d\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d285fd8f859c007aafe8f8b92f5c8b9b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\02a2dc3fb6f2f628d8f50a1ef0801c78\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fe79de89205f90529a69370bf3cb9e6c\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\02067b2103c072e242f8edf396ec5f20\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml:2:11-69
application
ADDED from C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml:4:5-21:19
INJECTED from C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml:4:5-21:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f7cfde7d9ac015d4e9e9435d414711\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f7cfde7d9ac015d4e9e9435d414711\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\0bdb13050b39e5ec5f87e73f51b7ba0d\transformed\ui-test-manifest-1.7.4\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\0bdb13050b39e5ec5f87e73f51b7ba0d\transformed\ui-test-manifest-1.7.4\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\1020d7831843ef581ff10fd389e83ec5\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\1020d7831843ef581ff10fd389e83ec5\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8021988f1600a50d6929200a80486d92\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8021988f1600a50d6929200a80486d92\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\02a2dc3fb6f2f628d8f50a1ef0801c78\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\02a2dc3fb6f2f628d8f50a1ef0801c78\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml:9:9-35
	android:label
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml:7:9-41
	android:roundIcon
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml:8:9-54
	android:icon
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml:6:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml:5:9-35
	android:theme
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml:10:9-42
activity#top.yogiczy.mytv.mobile.MainActivity
ADDED from C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml:11:9-20:20
	android:exported
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml:13:13-36
	android:theme
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml:14:13-46
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml:12:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml:15:13-19:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml:16:17-69
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml:16:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml:18:17-77
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml:18:27-74
uses-sdk
INJECTED from C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml
MERGED from [:core:data] C:\Users\<USER>\StudioProjects\mytv-android\core\data\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:core:data] C:\Users\<USER>\StudioProjects\mytv-android\core\data\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:core:designsystem] C:\Users\<USER>\StudioProjects\mytv-android\core\designsystem\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:core:designsystem] C:\Users\<USER>\StudioProjects\mytv-android\core\designsystem\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:core:util] C:\Users\<USER>\StudioProjects\mytv-android\core\util\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:core:util] C:\Users\<USER>\StudioProjects\mytv-android\core\util\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\018c63b1485334add67611329c38a7ad\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\018c63b1485334add67611329c38a7ad\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c98e1a41ddbd0f58bbf4b7505ad9616c\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c98e1a41ddbd0f58bbf4b7505ad9616c\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.8.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\e27b26ab4fdbef454305ada43ffa6aaf\transformed\navigation-common-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.8.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\e27b26ab4fdbef454305ada43ffa6aaf\transformed\navigation-common-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.8.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\00b00a57a1321b3abddaddf7bed980a7\transformed\navigation-runtime-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.8.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\00b00a57a1321b3abddaddf7bed980a7\transformed\navigation-runtime-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\901134bfa3027fdc4efc5e9b1dbbf70b\transformed\navigation-common-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\901134bfa3027fdc4efc5e9b1dbbf70b\transformed\navigation-common-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\a78bc4bbe8c8b7fc76a9ffad5cc4261e\transformed\navigation-runtime-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\a78bc4bbe8c8b7fc76a9ffad5cc4261e\transformed\navigation-runtime-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.8.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\eb7529dec08a16f0ba21b419b9e507fa\transformed\navigation-compose-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.8.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\eb7529dec08a16f0ba21b419b9e507fa\transformed\navigation-compose-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\136feb759fbdaa722f3905caf7f29ff7\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\136feb759fbdaa722f3905caf7f29ff7\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\30e8e953c8ca6396d05eae233b004a84\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\30e8e953c8ca6396d05eae233b004a84\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\790bca944b41d25834ca5cde0d5c6974\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\790bca944b41d25834ca5cde0d5c6974\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\fd2de9df1fcb7d47d0f664953b17c729\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\fd2de9df1fcb7d47d0f664953b17c729\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\a99e45a91607e1c71c206aab1452b5b3\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\a99e45a91607e1c71c206aab1452b5b3\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\b73b969236973d78657ede4f0f194d2a\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\b73b969236973d78657ede4f0f194d2a\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\b8b8e32440ebe6b1a27aee4fb611b8ab\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\b8b8e32440ebe6b1a27aee4fb611b8ab\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\2510da7f39df9715e457e0526689281c\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\2510da7f39df9715e457e0526689281c\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\db4a6c88d4347ce178fe3684539ea8e0\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\db4a6c88d4347ce178fe3684539ea8e0\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\ab891530b64eecdf0e7491d248519c0a\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\ab891530b64eecdf0e7491d248519c0a\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d8fa77734273d7df92383485cc746c5\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d8fa77734273d7df92383485cc746c5\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\7f33fb5b142e37b2e11aba2dc17a6b8a\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\7f33fb5b142e37b2e11aba2dc17a6b8a\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\fde23f3bbc10ce5c49e062b340e080e0\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\fde23f3bbc10ce5c49e062b340e080e0\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\2beb69d6875208f2d04111a35001bba9\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\2beb69d6875208f2d04111a35001bba9\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\e3284905063e776d62ae70595f5b34b4\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\e3284905063e776d62ae70595f5b34b4\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a112f10a8620d44c07292457d5e069f0\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a112f10a8620d44c07292457d5e069f0\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f7cfde7d9ac015d4e9e9435d414711\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f7cfde7d9ac015d4e9e9435d414711\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8d6da967e4281651358e6dd3dac6ee93\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8d6da967e4281651358e6dd3dac6ee93\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a24962cf41916db9572a74a797a0e06\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a24962cf41916db9572a74a797a0e06\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\30ac6839eb42bcb86357a0d144ffee2c\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\30ac6839eb42bcb86357a0d144ffee2c\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\06f54bc868185307d2008338de4b6f8a\transformed\lifecycle-livedata-core-ktx-2.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\06f54bc868185307d2008338de4b6f8a\transformed\lifecycle-livedata-core-ktx-2.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\5f73895031d02ca8d1d64315fa193d91\transformed\lifecycle-livedata-2.8.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\5f73895031d02ca8d1d64315fa193d91\transformed\lifecycle-livedata-2.8.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\a6c0d6aba12adda640fe295311b9cada\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\a6c0d6aba12adda640fe295311b9cada\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d35c56726b65064adaa3dd1f91e7d98\transformed\lifecycle-livedata-core-2.8.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d35c56726b65064adaa3dd1f91e7d98\transformed\lifecycle-livedata-core-2.8.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\d3e7b3daac682e3549d3763424bb713a\transformed\lifecycle-viewmodel-savedstate-2.8.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\d3e7b3daac682e3549d3763424bb713a\transformed\lifecycle-viewmodel-savedstate-2.8.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\63ccdacec2c798c3da3c085cba28b3e6\transformed\lifecycle-viewmodel-2.8.6\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\63ccdacec2c798c3da3c085cba28b3e6\transformed\lifecycle-viewmodel-2.8.6\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\bdcbc52a7f4fc574f82c6a94b68ef448\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\bdcbc52a7f4fc574f82c6a94b68ef448\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\c7360db8db21da0cd2a2513de2c76e03\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\c7360db8db21da0cd2a2513de2c76e03\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\afa94a096a9eeb83f65f48490bf80e41\transformed\lifecycle-viewmodel-ktx-2.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\afa94a096a9eeb83f65f48490bf80e41\transformed\lifecycle-viewmodel-ktx-2.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\4ac6608a66c236e16140b2df2b55713a\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\4ac6608a66c236e16140b2df2b55713a\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\bba6757b190126e7429206a459baebc4\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\bba6757b190126e7429206a459baebc4\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\0dbb727f01cc86fe24c49a72666e9e7f\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\0dbb727f01cc86fe24c49a72666e9e7f\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\4fe448b3d7d63514c1e70b7a8388f22f\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\4fe448b3d7d63514c1e70b7a8388f22f\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\f0ce41bc1ab5a7ccfdd795c249b2c5b2\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\f0ce41bc1ab5a7ccfdd795c249b2c5b2\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\0bdb13050b39e5ec5f87e73f51b7ba0d\transformed\ui-test-manifest-1.7.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\0bdb13050b39e5ec5f87e73f51b7ba0d\transformed\ui-test-manifest-1.7.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\1020d7831843ef581ff10fd389e83ec5\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\1020d7831843ef581ff10fd389e83ec5\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\7e7e1990e3a2927aa964ef667f1a3836\transformed\activity-compose-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\7e7e1990e3a2927aa964ef667f1a3836\transformed\activity-compose-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\ae30512adfd89f17d52d28ae78730ad7\transformed\activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\ae30512adfd89f17d52d28ae78730ad7\transformed\activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\4c124e34a7f4172b432f9fe272bec823\transformed\activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\4c124e34a7f4172b432f9fe272bec823\transformed\activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e1e116a809e646e532a718de75b1ceef\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e1e116a809e646e532a718de75b1ceef\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5bc5956aa20aa1b6e506265a26f6d087\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5bc5956aa20aa1b6e506265a26f6d087\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f73974b6797712fe76a28e8dd126afcc\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f73974b6797712fe76a28e8dd126afcc\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f40c40fb42cdb0ba22401bf8fc1df14\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f40c40fb42cdb0ba22401bf8fc1df14\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f1fecd21f0cb9107e9c3751c3fdde6b\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f1fecd21f0cb9107e9c3751c3fdde6b\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8341b782ba39e57543abacf09495b30f\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8341b782ba39e57543abacf09495b30f\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f1e35c9300b09aae36d857ae78ea9a9a\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f1e35c9300b09aae36d857ae78ea9a9a\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e8e26f56d8e20e616f6e0fb0878dba9\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e8e26f56d8e20e616f6e0fb0878dba9\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\db0874ac1ef75b4773055a57b3321661\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\db0874ac1ef75b4773055a57b3321661\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\876bb1e606565bc27f304f01793afc9b\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\876bb1e606565bc27f304f01793afc9b\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\4e9d2cb82d2fe484f7dcd235e8334edb\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\4e9d2cb82d2fe484f7dcd235e8334edb\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\aaf0057ac6b1dde88a2094d678e675b3\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\aaf0057ac6b1dde88a2094d678e675b3\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8021988f1600a50d6929200a80486d92\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8021988f1600a50d6929200a80486d92\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4545091883f438d426e29f97df13794d\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4545091883f438d426e29f97df13794d\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d285fd8f859c007aafe8f8b92f5c8b9b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d285fd8f859c007aafe8f8b92f5c8b9b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\02a2dc3fb6f2f628d8f50a1ef0801c78\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\02a2dc3fb6f2f628d8f50a1ef0801c78\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fe79de89205f90529a69370bf3cb9e6c\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fe79de89205f90529a69370bf3cb9e6c\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\02067b2103c072e242f8edf396ec5f20\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\02067b2103c072e242f8edf396ec5f20\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f7cfde7d9ac015d4e9e9435d414711\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\02a2dc3fb6f2f628d8f50a1ef0801c78\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\02a2dc3fb6f2f628d8f50a1ef0801c78\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f7cfde7d9ac015d4e9e9435d414711\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f7cfde7d9ac015d4e9e9435d414711\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f7cfde7d9ac015d4e9e9435d414711\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f7cfde7d9ac015d4e9e9435d414711\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f7cfde7d9ac015d4e9e9435d414711\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f7cfde7d9ac015d4e9e9435d414711\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f7cfde7d9ac015d4e9e9435d414711\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:30:17-78
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\0bdb13050b39e5ec5f87e73f51b7ba0d\transformed\ui-test-manifest-1.7.4\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\0bdb13050b39e5ec5f87e73f51b7ba0d\transformed\ui-test-manifest-1.7.4\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\0bdb13050b39e5ec5f87e73f51b7ba0d\transformed\ui-test-manifest-1.7.4\AndroidManifest.xml:24:13-63
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\1020d7831843ef581ff10fd389e83ec5\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\1020d7831843ef581ff10fd389e83ec5\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\1020d7831843ef581ff10fd389e83ec5\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#top.yogiczy.mytv.mobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#top.yogiczy.mytv.mobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
