<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":core:util" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-android\core\util\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":core:designsystem" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-android\core\designsystem\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":core:data" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-android\core\data\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config="io.github.abdallahmehiz:mpv-android-lib:0.1.9" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.9\transforms\dfda9b5f0a79a05127a5e51983ff9c49\transformed\mpv-android-lib-0.1.9\assets"><file name="cacert.pem" path="C:\Users\<USER>\.gradle\caches\8.9\transforms\dfda9b5f0a79a05127a5e51983ff9c49\transformed\mpv-android-lib-0.1.9\assets\cacert.pem"/><file name="subfont.ttf" path="C:\Users\<USER>\.gradle\caches\8.9\transforms\dfda9b5f0a79a05127a5e51983ff9c49\transformed\mpv-android-lib-0.1.9\assets\subfont.ttf"/></source></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\assets"><file name="remote-configs/assets/index-Bq1XwZez.js" path="C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\assets\remote-configs\assets\index-Bq1XwZez.js"/><file name="remote-configs/assets/style-kB29EKMR.css" path="C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\assets\remote-configs\assets\style-kB29EKMR.css"/><file name="remote-configs/index.html" path="C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\assets\remote-configs\index.html"/></source></dataSet><dataSet config="original" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-android\tv\src\original\assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-android\tv\src\debug\assets"/></dataSet><dataSet config="variant" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-android\tv\src\originalDebug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-android\tv\build\intermediates\shader_assets\originalDebug\compileOriginalDebugShaders\out"/></dataSet></merger>