1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="top.yogiczy.slcs.tv"
4    android:versionCode="2"
5    android:versionName="3.3.9" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10
11    <uses-feature
11-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:5:5-7:36
12        android:name="android.hardware.touchscreen"
12-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:6:9-52
13        android:required="false" />
13-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:7:9-33
14    <uses-feature
14-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:8:5-10:36
15        android:name="android.software.leanback"
15-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:9:9-49
16        android:required="false" />
16-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:10:9-33
17
18    <queries>
18-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:12:5-14:15
19        <package android:name="com.google.android.webview" />
19-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:13:9-62
19-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:13:18-59
20    </queries>
21
22    <uses-permission android:name="android.permission.INTERNET" />
22-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:16:5-67
22-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:16:22-64
23    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
23-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:17:5-81
23-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:17:22-78
24    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
24-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:18:5-83
24-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:18:22-80
25    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
25-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:19:5-80
25-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:19:22-77
26    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
26-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:20:5-22:40
26-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:21:9-66
27    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
27-->[com.github.CarGuo.GSYVideoPlayer:gsyvideoplayer-java:v11.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d86375b5c640382f7c6d50707927708a\transformed\gsyvideoplayer-java-v11.1.0\AndroidManifest.xml:7:5-79
27-->[com.github.CarGuo.GSYVideoPlayer:gsyvideoplayer-java:v11.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d86375b5c640382f7c6d50707927708a\transformed\gsyvideoplayer-java-v11.1.0\AndroidManifest.xml:7:22-76
28
29    <permission
29-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
30        android:name="top.yogiczy.slcs.tv.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
30-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
31        android:protectionLevel="signature" />
31-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
32
33    <uses-permission android:name="top.yogiczy.slcs.tv.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
33-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
33-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
34
35    <application
35-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:24:5-81:19
36        android:name="top.yogiczy.mytv.tv.MyTVApplication"
36-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:25:9-40
37        package="is.xyz.mpv"
37-->[io.github.abdallahmehiz:mpv-android-lib:0.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\dfda9b5f0a79a05127a5e51983ff9c49\transformed\mpv-android-lib-0.1.9\AndroidManifest.xml:8:9-29
38        android:allowBackup="true"
38-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:26:9-35
39        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
39-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
40        android:banner="@mipmap/tv_banner"
40-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:27:9-43
41        android:debuggable="true"
42        android:extractNativeLibs="true"
43        android:icon="@mipmap/ic_launcher"
43-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:28:9-43
44        android:label="@string/app_name"
44-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:29:9-41
45        android:networkSecurityConfig="@xml/network_security_config"
45-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:30:9-69
46        android:requestLegacyExternalStorage="true"
46-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:31:9-52
47        android:supportsRtl="true"
47-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:32:9-35
48        android:testOnly="true"
49        android:theme="@style/Theme.MyTV"
49-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:33:9-42
50        android:usesCleartextTraffic="true"
50-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:34:9-44
51        android:versionCode="1"
51-->[io.github.abdallahmehiz:mpv-android-lib:0.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\dfda9b5f0a79a05127a5e51983ff9c49\transformed\mpv-android-lib-0.1.9\AndroidManifest.xml:9:9-32
52        android:versionName="1.0" >
52-->[io.github.abdallahmehiz:mpv-android-lib:0.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\dfda9b5f0a79a05127a5e51983ff9c49\transformed\mpv-android-lib-0.1.9\AndroidManifest.xml:10:9-34
53        <activity
53-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:36:9-50:20
54            android:name="top.yogiczy.mytv.tv.MainActivity"
54-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:37:13-41
55            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation|screenLayout|keyboardHidden"
55-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:38:13-119
56            android:exported="true"
56-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:39:13-36
57            android:resizeableActivity="true"
57-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:40:13-46
58            android:screenOrientation="sensorLandscape"
58-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:41:13-56
59            android:supportsPictureInPicture="true" >
59-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:42:13-52
60            <intent-filter>
60-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:44:13-49:29
61                <action android:name="android.intent.action.MAIN" />
61-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:45:17-69
61-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:45:25-66
62
63                <category android:name="android.intent.category.LAUNCHER" />
63-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:47:17-77
63-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:47:27-74
64                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
64-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:48:17-86
64-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:48:27-83
65            </intent-filter>
66        </activity>
67        <activity android:name="top.yogiczy.mytv.tv.CrashHandlerActivity" />
67-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:52:9-58
67-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:52:19-55
68
69        <receiver
69-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:54:9-63:20
70            android:name="top.yogiczy.mytv.tv.BootReceiver"
70-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:55:13-41
71            android:enabled="true"
71-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:56:13-35
72            android:exported="false"
72-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:57:13-37
73            android:permission="android.permission.RECEIVE_BOOT_COMPLETED" >
73-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:58:13-75
74            <intent-filter>
74-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:59:13-62:29
75                <action android:name="android.intent.action.BOOT_COMPLETED" />
75-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:60:17-79
75-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:60:25-76
76
77                <category android:name="android.intent.category.DEFAULT" />
77-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:61:17-76
77-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:61:27-73
78            </intent-filter>
79        </receiver>
80
81        <service android:name="top.yogiczy.mytv.tv.HttpServerService" />
81-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:65:9-54
81-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:65:18-51
82
83        <provider
84            android:name="androidx.core.content.FileProvider"
84-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:68:13-62
85            android:authorities="top.yogiczy.slcs.tv.FileProvider"
85-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:69:13-64
86            android:exported="false"
86-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:70:13-37
87            android:grantUriPermissions="true" >
87-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:71:13-47
88            <meta-data
88-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:72:13-74:54
89                android:name="android.support.FILE_PROVIDER_PATHS"
89-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:73:17-67
90                android:resource="@xml/file_paths" />
90-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:74:17-51
91        </provider>
92
93        <meta-data
93-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:77:9-79:37
94            android:name="io.sentry.auto-init"
94-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:78:13-47
95            android:value="false" />
95-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:79:13-34
96
97        <!-- 'android:authorities' must be unique in the device, across all apps -->
98        <provider
98-->[io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:12:9-15:40
99            android:name="io.sentry.android.core.SentryInitProvider"
99-->[io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:13:13-69
100            android:authorities="top.yogiczy.slcs.tv.SentryInitProvider"
100-->[io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:14:13-70
101            android:exported="false" />
101-->[io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:15:13-37
102        <provider
102-->[io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:16:9-20:39
103            android:name="io.sentry.android.core.SentryPerformanceProvider"
103-->[io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:17:13-76
104            android:authorities="top.yogiczy.slcs.tv.SentryPerformanceProvider"
104-->[io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:18:13-77
105            android:exported="false"
105-->[io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:19:13-37
106            android:initOrder="200" />
106-->[io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:20:13-36
107        <provider
107-->[androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:24:9-32:20
108            android:name="androidx.startup.InitializationProvider"
108-->[androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:25:13-67
109            android:authorities="top.yogiczy.slcs.tv.androidx-startup"
109-->[androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:26:13-68
110            android:exported="false" >
110-->[androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:27:13-37
111            <meta-data
111-->[androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:29:13-31:52
112                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
112-->[androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:30:17-78
113                android:value="androidx.startup" />
113-->[androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:31:17-49
114            <meta-data
114-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f7cfde7d9ac015d4e9e9435d414711\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
115                android:name="androidx.emoji2.text.EmojiCompatInitializer"
115-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f7cfde7d9ac015d4e9e9435d414711\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
116                android:value="androidx.startup" />
116-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f7cfde7d9ac015d4e9e9435d414711\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
117            <meta-data
117-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
118                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
118-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
119                android:value="androidx.startup" />
119-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
120        </provider>
121
122        <activity
122-->[androidx.compose.ui:ui-test-manifest:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\0bdb13050b39e5ec5f87e73f51b7ba0d\transformed\ui-test-manifest-1.7.4\AndroidManifest.xml:23:9-25:39
123            android:name="androidx.activity.ComponentActivity"
123-->[androidx.compose.ui:ui-test-manifest:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\0bdb13050b39e5ec5f87e73f51b7ba0d\transformed\ui-test-manifest-1.7.4\AndroidManifest.xml:24:13-63
124            android:exported="true" />
124-->[androidx.compose.ui:ui-test-manifest:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\0bdb13050b39e5ec5f87e73f51b7ba0d\transformed\ui-test-manifest-1.7.4\AndroidManifest.xml:25:13-36
125        <activity
125-->[androidx.compose.ui:ui-tooling-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\1020d7831843ef581ff10fd389e83ec5\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
126            android:name="androidx.compose.ui.tooling.PreviewActivity"
126-->[androidx.compose.ui:ui-tooling-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\1020d7831843ef581ff10fd389e83ec5\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
127            android:exported="true" />
127-->[androidx.compose.ui:ui-tooling-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\1020d7831843ef581ff10fd389e83ec5\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
128
129        <receiver
129-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
130            android:name="androidx.profileinstaller.ProfileInstallReceiver"
130-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
131            android:directBootAware="false"
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
132            android:enabled="true"
132-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
133            android:exported="true"
133-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
134            android:permission="android.permission.DUMP" >
134-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
135            <intent-filter>
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
136                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
137            </intent-filter>
138            <intent-filter>
138-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
139                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
140            </intent-filter>
141            <intent-filter>
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
142                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
143            </intent-filter>
144            <intent-filter>
144-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
145                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
145-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
145-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
146            </intent-filter>
147        </receiver>
148    </application>
149
150</manifest>
