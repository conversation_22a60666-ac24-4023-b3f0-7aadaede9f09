{"logs": [{"outputFile": "top.yogiczy.mytv.tv-mergeOriginalDebugResources-75:/values-bn/values-bn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\55d105b608835fb0a5975933fd0070b6\\transformed\\core-1.13.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,358,461,562,664,784", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "149,251,353,456,557,659,779,880"}, "to": {"startLines": "46,47,48,49,50,51,52,119", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3471,3570,3672,3774,3877,3978,4080,9356", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "3565,3667,3769,3872,3973,4075,4195,9452"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\9db5af2f2458547656275ea3d5385dab\\transformed\\media3-exoplayer-1.4.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,189,256,322,397,464,596,725", "endColumns": "67,65,66,65,74,66,131,128,88", "endOffsets": "118,184,251,317,392,459,591,720,809"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6630,6698,6764,6831,6897,6972,7039,7171,7300", "endColumns": "67,65,66,65,74,66,131,128,88", "endOffsets": "6693,6759,6826,6892,6967,7034,7166,7295,7384"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\f0ce41bc1ab5a7ccfdd795c249b2c5b2\\transformed\\ui-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,283,373,471,557,636,742,829,918,996,1077,1160,1236,1313,1389,1464,1532", "endColumns": "93,83,89,97,85,78,105,86,88,77,80,82,75,76,75,74,67,117", "endOffsets": "194,278,368,466,552,631,737,824,913,991,1072,1155,1231,1308,1384,1459,1527,1645"}, "to": {"startLines": "53,54,55,56,57,108,109,110,111,112,113,115,116,117,118,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4200,4294,4378,4468,4566,8437,8516,8622,8709,8798,8876,9044,9127,9203,9280,9457,9532,9600", "endColumns": "93,83,89,97,85,78,105,86,88,77,80,82,75,76,75,74,67,117", "endOffsets": "4289,4373,4463,4561,4647,8511,8617,8704,8793,8871,8952,9122,9198,9275,9351,9527,9595,9713"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\5a79ec61513eb95e1266e907126a2152\\transformed\\media3-ui-1.4.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,497,691,776,860,941,1034,1130,1206,1272,1361,1450,1517,1581,1643,1716,1832,1948,2066,2137,2220,2289,2365,2453,2540,2604,2669,2722,2784,2832,2893,2953,3015,3079,3145,3202,3266,3331,3384,3447,3524,3601,3653", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,84,83,80,92,95,75,65,88,88,66,63,61,72,115,115,117,70,82,68,75,87,86,63,64,52,61,47,60,59,61,63,65,56,63,64,52,62,76,76,51,63", "endOffsets": "283,492,686,771,855,936,1029,1125,1201,1267,1356,1445,1512,1576,1638,1711,1827,1943,2061,2132,2215,2284,2360,2448,2535,2599,2664,2717,2779,2827,2888,2948,3010,3074,3140,3197,3261,3326,3379,3442,3519,3596,3648,3712"}, "to": {"startLines": "2,11,15,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,338,547,4652,4737,4821,4902,4995,5091,5167,5233,5322,5411,5478,5542,5604,5677,5793,5909,6027,6098,6181,6250,6326,6414,6501,6565,7389,7442,7504,7552,7613,7673,7735,7799,7865,7922,7986,8051,8104,8167,8244,8321,8373", "endLines": "10,14,18,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "17,12,12,84,83,80,92,95,75,65,88,88,66,63,61,72,115,115,117,70,82,68,75,87,86,63,64,52,61,47,60,59,61,63,65,56,63,64,52,62,76,76,51,63", "endOffsets": "333,542,736,4732,4816,4897,4990,5086,5162,5228,5317,5406,5473,5537,5599,5672,5788,5904,6022,6093,6176,6245,6321,6409,6496,6560,6625,7437,7499,7547,7608,7668,7730,7794,7860,7917,7981,8046,8099,8162,8239,8316,8368,8432"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\c98e1a41ddbd0f58bbf4b7505ad9616c\\transformed\\appcompat-1.7.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,425,514,619,740,823,905,996,1089,1183,1277,1377,1470,1565,1659,1750,1841,1927,2037,2141,2244,2352,2460,2565,2730,2835", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "208,314,420,509,614,735,818,900,991,1084,1178,1272,1372,1465,1560,1654,1745,1836,1922,2032,2136,2239,2347,2455,2560,2725,2830,2917"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "741,849,955,1061,1150,1255,1376,1459,1541,1632,1725,1819,1913,2013,2106,2201,2295,2386,2477,2563,2673,2777,2880,2988,3096,3201,3366,8957", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "844,950,1056,1145,1250,1371,1454,1536,1627,1720,1814,1908,2008,2101,2196,2290,2381,2472,2558,2668,2772,2875,2983,3091,3196,3361,3466,9039"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\fd2de9df1fcb7d47d0f664953b17c729\\transformed\\foundation-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,84", "endOffsets": "135,220"}, "to": {"startLines": "123,124", "startColumns": "4,4", "startOffsets": "9718,9803", "endColumns": "84,84", "endOffsets": "9798,9883"}}]}]}