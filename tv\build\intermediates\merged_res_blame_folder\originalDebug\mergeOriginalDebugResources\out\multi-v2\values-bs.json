{"logs": [{"outputFile": "top.yogiczy.mytv.tv-mergeOriginalDebugResources-75:/values-bs/values-bs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\55d105b608835fb0a5975933fd0070b6\\transformed\\core-1.13.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,457,561,663,780", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "148,250,348,452,556,658,775,876"}, "to": {"startLines": "48,49,50,51,52,53,54,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3615,3713,3815,3913,4017,4121,4223,9538", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "3708,3810,3908,4012,4116,4218,4335,9634"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\5a79ec61513eb95e1266e907126a2152\\transformed\\media3-ui-1.4.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,293,571,835,921,1008,1096,1194,1301,1371,1438,1534,1626,1691,1764,1827,1895,2009,2125,2241,2321,2405,2476,2547,2648,2750,2822,2892,2945,3003,3051,3112,3184,3251,3315,3386,3450,3509,3574,3626,3693,3774,3855,3911", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "17,12,12,85,86,87,97,106,69,66,95,91,64,72,62,67,113,115,115,79,83,70,70,100,101,71,69,52,57,47,60,71,66,63,70,63,58,64,51,66,80,80,55,67", "endOffsets": "288,566,830,916,1003,1091,1189,1296,1366,1433,1529,1621,1686,1759,1822,1890,2004,2120,2236,2316,2400,2471,2542,2643,2745,2817,2887,2940,2998,3046,3107,3179,3246,3310,3381,3445,3504,3569,3621,3688,3769,3850,3906,3974"}, "to": {"startLines": "2,11,16,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,343,621,4808,4894,4981,5069,5167,5274,5344,5411,5507,5599,5664,5737,5800,5868,5982,6098,6214,6294,6378,6449,6520,6621,6723,6795,7545,7598,7656,7704,7765,7837,7904,7968,8039,8103,8162,8227,8279,8346,8427,8508,8564", "endLines": "10,15,20,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "endColumns": "17,12,12,85,86,87,97,106,69,66,95,91,64,72,62,67,113,115,115,79,83,70,70,100,101,71,69,52,57,47,60,71,66,63,70,63,58,64,51,66,80,80,55,67", "endOffsets": "338,616,880,4889,4976,5064,5162,5269,5339,5406,5502,5594,5659,5732,5795,5863,5977,6093,6209,6289,6373,6444,6515,6616,6718,6790,6860,7593,7651,7699,7760,7832,7899,7963,8034,8098,8157,8222,8274,8341,8422,8503,8559,8627"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\f0ce41bc1ab5a7ccfdd795c249b2c5b2\\transformed\\ui-release\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,206,294,388,487,573,650,742,834,919,1000,1086,1159,1236,1315,1392,1472,1542", "endColumns": "100,87,93,98,85,76,91,91,84,80,85,72,76,78,76,79,69,117", "endOffsets": "201,289,383,482,568,645,737,829,914,995,1081,1154,1231,1310,1387,1467,1537,1655"}, "to": {"startLines": "55,56,57,58,59,110,111,112,113,114,115,117,118,119,120,122,123,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4340,4441,4529,4623,4722,8632,8709,8801,8893,8978,9059,9232,9305,9382,9461,9639,9719,9789", "endColumns": "100,87,93,98,85,76,91,91,84,80,85,72,76,78,76,79,69,117", "endOffsets": "4436,4524,4618,4717,4803,8704,8796,8888,8973,9054,9140,9300,9377,9456,9533,9714,9784,9902"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\fd2de9df1fcb7d47d0f664953b17c729\\transformed\\foundation-release\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,91", "endOffsets": "140,232"}, "to": {"startLines": "125,126", "startColumns": "4,4", "startOffsets": "9907,9997", "endColumns": "89,91", "endOffsets": "9992,10084"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\9db5af2f2458547656275ea3d5385dab\\transformed\\media3-exoplayer-1.4.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,191,256,329,408,481,581,662", "endColumns": "74,60,64,72,78,72,99,80,72", "endOffsets": "125,186,251,324,403,476,576,657,730"}, "to": {"startLines": "84,85,86,87,88,89,90,91,92", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6865,6940,7001,7066,7139,7218,7291,7391,7472", "endColumns": "74,60,64,72,78,72,99,80,72", "endOffsets": "6935,6996,7061,7134,7213,7286,7386,7467,7540"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\c98e1a41ddbd0f58bbf4b7505ad9616c\\transformed\\appcompat-1.7.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,323,430,516,620,742,827,909,1000,1093,1188,1282,1382,1475,1570,1665,1756,1847,1935,2038,2142,2248,2353,2467,2570,2739,2835", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,105,104,113,102,168,95,86", "endOffsets": "221,318,425,511,615,737,822,904,995,1088,1183,1277,1377,1470,1565,1660,1751,1842,1930,2033,2137,2243,2348,2462,2565,2734,2830,2917"}, "to": {"startLines": "21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "885,1006,1103,1210,1296,1400,1522,1607,1689,1780,1873,1968,2062,2162,2255,2350,2445,2536,2627,2715,2818,2922,3028,3133,3247,3350,3519,9145", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,105,104,113,102,168,95,86", "endOffsets": "1001,1098,1205,1291,1395,1517,1602,1684,1775,1868,1963,2057,2157,2250,2345,2440,2531,2622,2710,2813,2917,3023,3128,3242,3345,3514,3610,9227"}}]}]}