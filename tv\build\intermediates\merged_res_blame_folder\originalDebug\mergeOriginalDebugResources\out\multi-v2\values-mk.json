{"logs": [{"outputFile": "top.yogiczy.mytv.tv-mergeOriginalDebugResources-75:/values-mk/values-mk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\9db5af2f2458547656275ea3d5385dab\\transformed\\media3-exoplayer-1.4.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,186,250,318,395,468,557,642", "endColumns": "69,60,63,67,76,72,88,84,77", "endOffsets": "120,181,245,313,390,463,552,637,715"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6697,6767,6828,6892,6960,7037,7110,7199,7284", "endColumns": "69,60,63,67,76,72,88,84,77", "endOffsets": "6762,6823,6887,6955,7032,7105,7194,7279,7357"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\fd2de9df1fcb7d47d0f664953b17c729\\transformed\\foundation-release\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,149", "endColumns": "93,95", "endOffsets": "144,240"}, "to": {"startLines": "123,124", "startColumns": "4,4", "startOffsets": "9716,9810", "endColumns": "93,95", "endOffsets": "9805,9901"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\f0ce41bc1ab5a7ccfdd795c249b2c5b2\\transformed\\ui-release\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,299,395,498,583,660,750,842,926,1010,1099,1171,1248,1326,1402,1483,1554", "endColumns": "103,89,95,102,84,76,89,91,83,83,88,71,76,77,75,80,70,120", "endOffsets": "204,294,390,493,578,655,745,837,921,1005,1094,1166,1243,1321,1397,1478,1549,1670"}, "to": {"startLines": "53,54,55,56,57,108,109,110,111,112,113,115,116,117,118,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4186,4290,4380,4476,4579,8435,8512,8602,8694,8778,8862,9039,9111,9188,9266,9443,9524,9595", "endColumns": "103,89,95,102,84,76,89,91,83,83,88,71,76,77,75,80,70,120", "endOffsets": "4285,4375,4471,4574,4659,8507,8597,8689,8773,8857,8946,9106,9183,9261,9337,9519,9590,9711"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\5a79ec61513eb95e1266e907126a2152\\transformed\\media3-ui-1.4.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,311,516,702,791,881,962,1052,1143,1220,1285,1388,1493,1558,1622,1685,1757,1875,1991,2106,2183,2272,2343,2422,2512,2603,2667,2735,2788,2846,2894,2955,3021,3088,3151,3221,3285,3343,3409,3461,3526,3605,3684,3740", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,88,89,80,89,90,76,64,102,104,64,63,62,71,117,115,114,76,88,70,78,89,90,63,67,52,57,47,60,65,66,62,69,63,57,65,51,64,78,78,55,67", "endOffsets": "306,511,697,786,876,957,1047,1138,1215,1280,1383,1488,1553,1617,1680,1752,1870,1986,2101,2178,2267,2338,2417,2507,2598,2662,2730,2783,2841,2889,2950,3016,3083,3146,3216,3280,3338,3404,3456,3521,3600,3679,3735,3803"}, "to": {"startLines": "2,11,15,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,361,566,4664,4753,4843,4924,5014,5105,5182,5247,5350,5455,5520,5584,5647,5719,5837,5953,6068,6145,6234,6305,6384,6474,6565,6629,7362,7415,7473,7521,7582,7648,7715,7778,7848,7912,7970,8036,8088,8153,8232,8311,8367", "endLines": "10,14,18,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "17,12,12,88,89,80,89,90,76,64,102,104,64,63,62,71,117,115,114,76,88,70,78,89,90,63,67,52,57,47,60,65,66,62,69,63,57,65,51,64,78,78,55,67", "endOffsets": "356,561,747,4748,4838,4919,5009,5100,5177,5242,5345,5450,5515,5579,5642,5714,5832,5948,6063,6140,6229,6300,6379,6469,6560,6624,6692,7410,7468,7516,7577,7643,7710,7773,7843,7907,7965,8031,8083,8148,8227,8306,8362,8430"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\c98e1a41ddbd0f58bbf4b7505ad9616c\\transformed\\appcompat-1.7.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,425,511,619,738,822,903,994,1087,1183,1277,1377,1470,1565,1661,1752,1843,1930,2036,2142,2243,2350,2462,2566,2722,2820", "endColumns": "107,103,107,85,107,118,83,80,90,92,95,93,99,92,94,95,90,90,86,105,105,100,106,111,103,155,97,87", "endOffsets": "208,312,420,506,614,733,817,898,989,1082,1178,1272,1372,1465,1560,1656,1747,1838,1925,2031,2137,2238,2345,2457,2561,2717,2815,2903"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "752,860,964,1072,1158,1266,1385,1469,1550,1641,1734,1830,1924,2024,2117,2212,2308,2399,2490,2577,2683,2789,2890,2997,3109,3213,3369,8951", "endColumns": "107,103,107,85,107,118,83,80,90,92,95,93,99,92,94,95,90,90,86,105,105,100,106,111,103,155,97,87", "endOffsets": "855,959,1067,1153,1261,1380,1464,1545,1636,1729,1825,1919,2019,2112,2207,2303,2394,2485,2572,2678,2784,2885,2992,3104,3208,3364,3462,9034"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\55d105b608835fb0a5975933fd0070b6\\transformed\\core-1.13.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,450,555,658,774", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "148,250,347,445,550,653,769,870"}, "to": {"startLines": "46,47,48,49,50,51,52,119", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3467,3565,3667,3764,3862,3967,4070,9342", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "3560,3662,3759,3857,3962,4065,4181,9438"}}]}]}