{"logs": [{"outputFile": "top.yogiczy.mytv.tv-mergeOriginalDebugResources-75:/values-pt/values-pt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\fd2de9df1fcb7d47d0f664953b17c729\\transformed\\foundation-release\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,138", "endColumns": "82,84", "endOffsets": "133,218"}, "to": {"startLines": "123,124", "startColumns": "4,4", "startOffsets": "9702,9785", "endColumns": "82,84", "endOffsets": "9780,9865"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\5a79ec61513eb95e1266e907126a2152\\transformed\\media3-ui-1.4.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,471,650,731,814,887,986,1082,1156,1222,1318,1413,1479,1548,1615,1686,1804,1921,2042,2109,2195,2271,2345,2443,2543,2607,2671,2724,2782,2830,2891,2956,3018,3084,3154,3218,3279,3345,3398,3462,3540,3618,3677", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,80,82,72,98,95,73,65,95,94,65,68,66,70,117,116,120,66,85,75,73,97,99,63,63,52,57,47,60,64,61,65,69,63,60,65,52,63,77,77,58,70", "endOffsets": "280,466,645,726,809,882,981,1077,1151,1217,1313,1408,1474,1543,1610,1681,1799,1916,2037,2104,2190,2266,2340,2438,2538,2602,2666,2719,2777,2825,2886,2951,3013,3079,3149,3213,3274,3340,3393,3457,3535,3613,3672,3743"}, "to": {"startLines": "2,11,15,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,521,4636,4717,4800,4873,4972,5068,5142,5208,5304,5399,5465,5534,5601,5672,5790,5907,6028,6095,6181,6257,6331,6429,6529,6593,7343,7396,7454,7502,7563,7628,7690,7756,7826,7890,7951,8017,8070,8134,8212,8290,8349", "endLines": "10,14,18,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "17,12,12,80,82,72,98,95,73,65,95,94,65,68,66,70,117,116,120,66,85,75,73,97,99,63,63,52,57,47,60,64,61,65,69,63,60,65,52,63,77,77,58,70", "endOffsets": "330,516,695,4712,4795,4868,4967,5063,5137,5203,5299,5394,5460,5529,5596,5667,5785,5902,6023,6090,6176,6252,6326,6424,6524,6588,6652,7391,7449,7497,7558,7623,7685,7751,7821,7885,7946,8012,8065,8129,8207,8285,8344,8415"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\f0ce41bc1ab5a7ccfdd795c249b2c5b2\\transformed\\ui-release\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,286,383,482,568,651,748,839,926,1011,1101,1177,1253,1332,1407,1483,1550", "endColumns": "94,85,96,98,85,82,96,90,86,84,89,75,75,78,74,75,66,112", "endOffsets": "195,281,378,477,563,646,743,834,921,1006,1096,1172,1248,1327,1402,1478,1545,1658"}, "to": {"startLines": "53,54,55,56,57,108,109,110,111,112,113,115,116,117,118,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4173,4268,4354,4451,4550,8420,8503,8600,8691,8778,8863,9039,9115,9191,9270,9446,9522,9589", "endColumns": "94,85,96,98,85,82,96,90,86,84,89,75,75,78,74,75,66,112", "endOffsets": "4263,4349,4446,4545,4631,8498,8595,8686,8773,8858,8948,9110,9186,9265,9340,9517,9584,9697"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\9db5af2f2458547656275ea3d5385dab\\transformed\\media3-exoplayer-1.4.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,195,267,333,410,477,578,671", "endColumns": "69,69,71,65,76,66,100,92,69", "endOffsets": "120,190,262,328,405,472,573,666,736"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6657,6727,6797,6869,6935,7012,7079,7180,7273", "endColumns": "69,69,71,65,76,66,100,92,69", "endOffsets": "6722,6792,6864,6930,7007,7074,7175,7268,7338"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\c98e1a41ddbd0f58bbf4b7505ad9616c\\transformed\\appcompat-1.7.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "700,820,926,1033,1122,1223,1342,1427,1507,1598,1691,1786,1880,1980,2073,2168,2263,2354,2445,2530,2637,2748,2850,2958,3066,3176,3338,8953", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "815,921,1028,1117,1218,1337,1422,1502,1593,1686,1781,1875,1975,2068,2163,2258,2349,2440,2525,2632,2743,2845,2953,3061,3171,3333,3433,9034"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\55d105b608835fb0a5975933fd0070b6\\transformed\\core-1.13.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "46,47,48,49,50,51,52,119", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3438,3535,3637,3736,3836,3943,4053,9345", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "3530,3632,3731,3831,3938,4048,4168,9441"}}]}]}