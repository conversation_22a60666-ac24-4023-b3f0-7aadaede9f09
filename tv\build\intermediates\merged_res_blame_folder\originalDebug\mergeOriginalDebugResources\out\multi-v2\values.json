{"logs": [{"outputFile": "top.yogiczy.mytv.tv-mergeOriginalDebugResources-75:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\e27b26ab4fdbef454305ada43ffa6aaf\\transformed\\navigation-common-2.8.3\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "3474,3487,3493,3499,3508", "startColumns": "4,4,4,4,4", "startOffsets": "192357,192996,193240,193487,193850", "endLines": "3486,3492,3498,3501,3512", "endColumns": "24,24,24,24,24", "endOffsets": "192991,193235,193482,193615,194027"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\55d105b608835fb0a5975933fd0070b6\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "42,113,114,128,129,159,160,265,266,267,268,269,270,271,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,394,395,396,492,493,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,540,578,579,580,581,582,583,584,657,2201,2202,2207,2210,2215,2361,2362,3039,3073,3145,3178,3208,3241", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1751,5257,5329,6433,6498,8333,8402,15502,15572,15640,15712,15782,15843,15917,19239,19300,19361,19423,19487,19549,19610,19678,19778,19838,19904,19977,20046,20103,20155,24976,25048,25124,30047,30082,30731,30786,30849,30904,30962,31020,31081,31144,31201,31252,31302,31363,31420,31486,31520,31555,32611,35239,35306,35378,35447,35516,35590,35662,40901,141585,141702,141969,142262,142529,154102,154174,176977,178550,181356,183087,184087,184769", "endLines": "42,113,114,128,129,159,160,265,266,267,268,269,270,271,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,394,395,396,492,493,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,540,578,579,580,581,582,583,584,657,2201,2205,2207,2213,2215,2361,2362,3044,3082,3177,3198,3240,3246", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1806,5324,5412,6493,6559,8397,8460,15567,15635,15707,15777,15838,15912,15985,19295,19356,19418,19482,19544,19605,19673,19773,19833,19899,19972,20041,20098,20150,20212,25043,25119,25184,30077,30112,30781,30844,30899,30957,31015,31076,31139,31196,31247,31297,31358,31415,31481,31515,31550,31585,32676,35301,35373,35442,35511,35585,35657,35745,40967,141697,141898,142074,142458,142653,154169,154236,177175,178846,183082,183763,184764,184931"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\f177d3a1a8257db47f029f8294674ce4\\transformed\\gsyijkjava-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "36", "endOffsets": "87"}, "to": {"startLines": "642", "startColumns": "4", "startOffsets": "40181", "endColumns": "36", "endOffsets": "40213"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\4d3998365c79e1c032d449e3242957ec\\transformed\\coil-base-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "436", "startColumns": "4", "startOffsets": "27459", "endColumns": "49", "endOffsets": "27504"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\12797d7726951b9a1c4e5b131b725b3c\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "484,485,496,501,502,522,523,524,525,526", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "29634,29674,30232,30480,30535,31590,31644,31696,31745,31806", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "29669,29716,30270,30530,30577,31639,31691,31740,31801,31851"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\00b00a57a1321b3abddaddf7bed980a7\\transformed\\navigation-runtime-2.8.3\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "495,2488,3502,3505", "startColumns": "4,4,4,4", "startOffsets": "30179,158959,193620,193735", "endLines": "495,2494,3504,3507", "endColumns": "52,24,24,24", "endOffsets": "30227,159258,193730,193845"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\bdcbc52a7f4fc574f82c6a94b68ef448\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "531", "startColumns": "4", "startOffsets": "32045", "endColumns": "49", "endOffsets": "32090"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\fd2de9df1fcb7d47d0f664953b17c729\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "664,665", "startColumns": "4,4", "startOffsets": "41318,41374", "endColumns": "55,54", "endOffsets": "41369,41424"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\3a07bceaa23ef51ac9866ac1768a7c38\\transformed\\androidsvg-aar-1.4\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "141", "endOffsets": "192"}, "to": {"startLines": "3652", "startColumns": "4", "startOffsets": "199003", "endColumns": "141", "endOffsets": "199140"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\d86375b5c640382f7c6d50707927708a\\transformed\\gsyvideoplayer-java-v11.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,22,28,39,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,157,204,251,315,378,418,459,496,534,573,616,658,722,776,831,1078,1460,2026,2450", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,21,27,38,46,52", "endColumns": "55,45,46,46,63,62,39,40,36,37,38,42,41,63,53,54,12,12,12,24,24", "endOffsets": "106,152,199,246,310,373,413,454,491,529,568,611,653,717,771,826,1073,1455,2021,2445,2732"}, "to": {"startLines": "119,176,264,337,346,347,433,434,483,503,645,647,648,661,662,663,2363,2367,2373,3906,3914", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5736,9553,15455,20217,20717,20781,27304,27344,29597,30582,40330,40431,40474,41145,41209,41263,154241,154488,154870,208833,209257", "endLines": "119,176,264,337,346,347,433,434,483,503,645,647,648,661,662,663,2366,2372,2383,3913,3919", "endColumns": "55,45,46,46,63,62,39,40,36,37,38,42,41,63,53,54,12,12,12,24,24", "endOffsets": "5787,9594,15497,20259,20776,20839,27339,27380,29629,30615,40364,40469,40511,41204,41258,41313,154483,154865,155431,209252,209539"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\2beb69d6875208f2d04111a35001bba9\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "486", "startColumns": "4", "startOffsets": "29721", "endColumns": "65", "endOffsets": "29782"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\bd501f6f99f84825091a2bbfc5614134\\transformed\\media-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,5,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,288,410,476,598,659,725", "endColumns": "88,61,65,121,60,65,66", "endOffsets": "139,345,471,593,654,720,787"}, "to": {"startLines": "161,494,2206,2208,2209,2214,2216", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "8465,30117,141903,142079,142201,142463,142658", "endColumns": "88,61,65,121,60,65,66", "endOffsets": "8549,30174,141964,142196,142257,142524,142720"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\5a79ec61513eb95e1266e907126a2152\\transformed\\media3-ui-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,11,12,13,14,19,20,21,25,26,27,28,29,30,31,32,33,34,38,39,40,41,42,43,44,45,46,47,48,49,54,61,62,63,64,65,66,67,72,73,74,75,76,77,78,79,80,81,82,83,84,85,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,235,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,287,291,295,299,303,307,311,315,316,322,333,337,341,345,349,353,357,361,365,369,373,377,390,395,400,405,418,426,436,440,444,448,451,467,493,538", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,385,439,493,656,702,751,877,926,975,1034,1088,1143,1203,1262,1314,1364,1492,1557,1605,1654,1702,1759,1806,1861,1913,1967,2021,2075,2223,2461,2511,2560,2621,2681,2737,2797,2967,3027,3080,3137,3192,3248,3305,3354,3405,3460,3514,3573,3629,3684,3971,4036,4094,4143,4191,4242,4288,4345,4402,4464,4531,4603,4647,4704,4760,4823,4896,4966,5025,5082,5129,5184,5229,5278,5333,5387,5437,5488,5542,5601,5651,5709,5765,5818,5881,5946,6009,6061,6121,6185,6251,6309,6381,6442,6512,6582,6647,6712,6783,6878,6983,7086,7167,7250,7331,7420,7513,7606,7699,7784,7879,7972,8049,8141,8219,8299,8377,8463,8545,8638,8716,8807,8888,8977,9080,9181,9265,9361,9458,9553,9646,9738,9831,9924,10017,10100,10187,10282,10375,10477,10569,10650,10745,10838,10915,10959,11000,11045,11093,11137,11180,11229,11276,11320,11376,11429,11471,11518,11566,11626,11664,11714,11758,11797,11847,11899,11937,11984,12031,12072,12111,12149,12193,12241,12283,12321,12363,12417,12464,12501,12550,12592,12633,12674,12716,12759,12797,12833,12911,12989,13286,13556,13638,13720,13862,13940,14027,14112,14179,14242,14334,14426,14491,14554,14616,14687,14797,14908,15018,15085,15165,15236,15303,15388,15473,15536,15624,15688,15830,15930,15978,16121,16184,16246,16311,16382,16440,16498,16564,16616,16678,16754,16830,16884,16997,17276,17507,17717,17930,18140,18362,18578,18782,18820,19174,19961,20202,20442,20699,20952,21205,21440,21687,21926,22170,22391,22586,23258,23549,23845,24148,24814,25348,25822,26033,26233,26409,26517,27093,28038,29633", "endLines": "10,11,12,13,18,19,20,24,25,26,27,28,29,30,31,32,33,37,38,39,40,41,42,43,44,45,46,47,48,53,60,61,62,63,64,65,66,71,72,73,74,75,76,77,78,79,80,81,82,83,84,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,234,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,286,290,294,298,302,306,310,314,315,321,332,336,340,344,348,352,356,360,364,368,372,376,389,394,399,404,417,425,435,439,443,447,450,466,492,537,594", "endColumns": "17,49,53,53,9,45,48,9,48,48,58,53,54,59,58,51,49,9,64,47,48,47,56,46,54,51,53,53,53,9,9,49,48,60,59,55,59,9,59,52,56,54,55,56,48,50,54,53,58,55,54,9,64,57,48,47,50,45,56,56,61,66,71,43,56,55,62,72,69,58,56,46,54,44,48,54,53,49,50,53,58,49,57,55,52,62,64,62,51,59,63,65,57,71,60,69,69,64,64,70,94,104,102,80,82,80,88,92,92,92,84,94,92,76,91,77,79,77,85,81,92,77,90,80,88,102,100,83,95,96,94,92,91,92,92,92,82,86,94,92,101,91,80,94,92,76,43,40,44,47,43,42,48,46,43,55,52,41,46,47,59,37,49,43,38,49,51,37,46,46,40,38,37,43,47,41,37,41,53,46,36,48,41,40,40,41,42,37,35,77,77,12,12,81,81,141,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,87,63,141,99,47,142,62,61,64,70,57,57,65,51,61,75,75,53,112,10,10,10,10,10,10,10,10,37,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,22,22,22,22,22", "endOffsets": "330,380,434,488,651,697,746,872,921,970,1029,1083,1138,1198,1257,1309,1359,1487,1552,1600,1649,1697,1754,1801,1856,1908,1962,2016,2070,2218,2456,2506,2555,2616,2676,2732,2792,2962,3022,3075,3132,3187,3243,3300,3349,3400,3455,3509,3568,3624,3679,3966,4031,4089,4138,4186,4237,4283,4340,4397,4459,4526,4598,4642,4699,4755,4818,4891,4961,5020,5077,5124,5179,5224,5273,5328,5382,5432,5483,5537,5596,5646,5704,5760,5813,5876,5941,6004,6056,6116,6180,6246,6304,6376,6437,6507,6577,6642,6707,6778,6873,6978,7081,7162,7245,7326,7415,7508,7601,7694,7779,7874,7967,8044,8136,8214,8294,8372,8458,8540,8633,8711,8802,8883,8972,9075,9176,9260,9356,9453,9548,9641,9733,9826,9919,10012,10095,10182,10277,10370,10472,10564,10645,10740,10833,10910,10954,10995,11040,11088,11132,11175,11224,11271,11315,11371,11424,11466,11513,11561,11621,11659,11709,11753,11792,11842,11894,11932,11979,12026,12067,12106,12144,12188,12236,12278,12316,12358,12412,12459,12496,12545,12587,12628,12669,12711,12754,12792,12828,12906,12984,13281,13551,13633,13715,13857,13935,14022,14107,14174,14237,14329,14421,14486,14549,14611,14682,14792,14903,15013,15080,15160,15231,15298,15383,15468,15531,15619,15683,15825,15925,15973,16116,16179,16241,16306,16377,16435,16493,16559,16611,16673,16749,16825,16879,16992,17271,17502,17712,17925,18135,18357,18573,18777,18815,19169,19956,20197,20437,20694,20947,21200,21435,21682,21921,22165,22386,22581,23253,23544,23840,24143,24809,25343,25817,26028,26228,26404,26512,27088,28033,29628,31569"}, "to": {"startLines": "2,11,12,13,14,19,20,21,25,26,27,28,30,31,32,34,35,36,41,43,44,45,46,47,48,49,51,52,53,54,59,66,67,68,69,70,71,72,77,78,79,80,81,82,83,84,85,86,87,88,89,90,97,99,100,101,102,103,136,137,138,139,140,141,142,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,538,539,541,545,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,1757,1762,1766,1770,1774,1778,1782,1786,1790,1791,1797,1808,1812,1816,1820,1824,1828,1832,1836,1840,1844,1848,1852,1865,1870,1875,1880,1893,1901,1911,1915,1919,3032,3111,3247,3513,3558", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,430,484,538,701,747,796,922,971,1020,1079,1188,1243,1303,1407,1459,1509,1686,1811,1859,1908,1956,2013,2060,2115,2223,2277,2331,2385,2533,2771,2821,2870,2931,2991,3047,3107,3277,3337,3390,3447,3502,3558,3615,3664,3715,3770,3824,3883,3939,3994,4281,4387,4445,4494,4542,4593,6965,7022,7079,7141,7208,7280,7324,16163,16219,16282,16355,16425,16484,16541,16588,16643,16688,16737,16792,16846,16896,16947,17001,17060,17110,17168,17224,17277,17340,17405,17468,17520,17580,17644,17710,17768,17840,17901,17971,18041,18106,18171,20844,20939,21044,21147,21228,21311,21392,21481,21574,21667,21760,21845,21940,22033,22110,22202,22280,22360,22438,22524,22606,22699,22777,22868,22949,23038,23141,23242,23326,23422,23519,23614,23707,23799,23892,23985,24078,24161,24248,24343,24436,24538,24630,24711,24806,24899,27622,27666,27707,27752,27800,27844,27887,27936,27983,28027,28083,28136,28178,28225,28273,28333,28371,28421,28465,28504,28554,28606,28644,28691,28738,28779,28818,28856,28900,28948,28990,29028,29070,29124,29171,29208,29257,29299,29340,29381,29423,29466,29504,32455,32533,32681,32978,36094,36176,36258,36400,36478,36565,36650,36717,36780,36872,36964,37029,37092,37154,37225,37335,37446,37556,37623,37703,37774,37841,37926,38011,38074,38162,38872,39014,39114,39162,39305,39368,39430,39495,39566,39624,39682,39748,39800,39862,39938,40014,40068,110981,111260,111491,111701,111914,112124,112346,112562,112766,112804,113158,113945,114186,114426,114683,114936,115189,115424,115671,115910,116154,116375,116570,117242,117533,117829,118132,118798,119332,119806,120017,120217,176734,180208,184936,194032,195627", "endLines": "10,11,12,13,18,19,20,24,25,26,27,28,30,31,32,34,35,39,41,43,44,45,46,47,48,49,51,52,53,58,65,66,67,68,69,70,71,76,77,78,79,80,81,82,83,84,85,86,87,88,89,96,97,99,100,101,102,103,136,137,138,139,140,141,142,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,538,539,544,548,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,1761,1765,1769,1773,1777,1781,1785,1789,1790,1796,1807,1811,1815,1819,1823,1827,1831,1835,1839,1843,1847,1851,1864,1869,1874,1879,1892,1900,1910,1914,1918,1922,3034,3126,3272,3557,3614", "endColumns": "17,49,53,53,9,45,48,9,48,48,58,53,54,59,58,51,49,9,64,47,48,47,56,46,54,51,53,53,53,9,9,49,48,60,59,55,59,9,59,52,56,54,55,56,48,50,54,53,58,55,54,9,64,57,48,47,50,45,56,56,61,66,71,43,56,55,62,72,69,58,56,46,54,44,48,54,53,49,50,53,58,49,57,55,52,62,64,62,51,59,63,65,57,71,60,69,69,64,64,70,94,104,102,80,82,80,88,92,92,92,84,94,92,76,91,77,79,77,85,81,92,77,90,80,88,102,100,83,95,96,94,92,91,92,92,92,82,86,94,92,101,91,80,94,92,76,43,40,44,47,43,42,48,46,43,55,52,41,46,47,59,37,49,43,38,49,51,37,46,46,40,38,37,43,47,41,37,41,53,46,36,48,41,40,40,41,42,37,35,77,77,12,12,81,81,141,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,87,63,141,99,47,142,62,61,64,70,57,57,65,51,61,75,75,53,112,10,10,10,10,10,10,10,10,37,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,22,22,22,22,22", "endOffsets": "375,425,479,533,696,742,791,917,966,1015,1074,1128,1238,1298,1357,1454,1504,1632,1746,1854,1903,1951,2008,2055,2110,2162,2272,2326,2380,2528,2766,2816,2865,2926,2986,3042,3102,3272,3332,3385,3442,3497,3553,3610,3659,3710,3765,3819,3878,3934,3989,4276,4341,4440,4489,4537,4588,4634,7017,7074,7136,7203,7275,7319,7376,16214,16277,16350,16420,16479,16536,16583,16638,16683,16732,16787,16841,16891,16942,16996,17055,17105,17163,17219,17272,17335,17400,17463,17515,17575,17639,17705,17763,17835,17896,17966,18036,18101,18166,18237,20934,21039,21142,21223,21306,21387,21476,21569,21662,21755,21840,21935,22028,22105,22197,22275,22355,22433,22519,22601,22694,22772,22863,22944,23033,23136,23237,23321,23417,23514,23609,23702,23794,23887,23980,24073,24156,24243,24338,24431,24533,24625,24706,24801,24894,24971,27661,27702,27747,27795,27839,27882,27931,27978,28022,28078,28131,28173,28220,28268,28328,28366,28416,28460,28499,28549,28601,28639,28686,28733,28774,28813,28851,28895,28943,28985,29023,29065,29119,29166,29203,29252,29294,29335,29376,29418,29461,29499,29535,32528,32606,32973,33243,36171,36253,36395,36473,36560,36645,36712,36775,36867,36959,37024,37087,37149,37220,37330,37441,37551,37618,37698,37769,37836,37921,38006,38069,38157,38221,39009,39109,39157,39300,39363,39425,39490,39561,39619,39677,39743,39795,39857,39933,40009,40063,40176,111255,111486,111696,111909,112119,112341,112557,112761,112799,113153,113940,114181,114421,114678,114931,115184,115419,115666,115905,116149,116370,116565,117237,117528,117824,118127,118793,119327,119801,120012,120212,120388,176837,180779,185876,195622,197563"}}, {"source": "C:\\Users\\<USER>\\StudioProjects\\mytv-android\\tv\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "19", "endColumns": "82", "endOffsets": "97"}, "to": {"startLines": "2253", "startColumns": "4", "startOffsets": "145361", "endColumns": "81", "endOffsets": "145438"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\effa78477564d01358468880b73f82a7\\transformed\\recyclerview-1.3.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,170,218,274,349,425,497,563", "endLines": "2,3,4,5,6,7,8,9,30", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "106,165,213,269,344,420,492,558,1398"}, "to": {"startLines": "50,309,310,311,319,320,321,491,3631", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "2167,18242,18301,18349,19016,19091,19167,29981,198163", "endLines": "50,309,310,311,319,320,321,491,3651", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "2218,18296,18344,18400,19086,19162,19234,30042,198998"}}, {"source": "C:\\Users\\<USER>\\StudioProjects\\mytv-android\\tv\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "17", "endColumns": "41", "endOffsets": "54"}, "to": {"startLines": "577", "startColumns": "4", "startOffsets": "35197", "endColumns": "41", "endOffsets": "35234"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\c98e1a41ddbd0f58bbf4b7505ad9616c\\transformed\\appcompat-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "29,33,40,98,104,105,106,107,108,109,110,111,112,115,116,117,118,120,121,122,123,124,125,126,127,130,131,132,133,134,135,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,162,163,164,165,166,167,168,169,170,171,172,173,174,175,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,272,273,312,313,314,315,316,317,318,338,339,340,341,342,343,344,345,429,430,431,432,488,498,499,505,527,534,535,536,537,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,652,666,667,668,669,670,671,679,680,684,688,692,697,703,710,714,718,723,727,731,735,739,743,747,753,757,763,767,773,777,782,786,789,793,799,803,809,813,819,822,826,830,834,838,842,843,844,845,848,851,854,857,861,862,863,864,865,868,870,872,874,879,880,884,890,894,895,897,909,910,914,920,924,925,926,930,957,961,962,966,994,1166,1192,1363,1389,1420,1428,1434,1450,1472,1477,1482,1492,1501,1510,1514,1521,1540,1547,1548,1557,1560,1563,1567,1571,1575,1578,1579,1584,1589,1599,1604,1611,1617,1618,1621,1625,1630,1632,1634,1637,1640,1642,1646,1649,1656,1659,1662,1666,1668,1672,1674,1676,1678,1682,1690,1698,1710,1716,1725,1728,1739,1742,1743,1748,1749,1930,1999,2069,2070,2080,2089,2090,2092,2096,2099,2102,2105,2108,2111,2114,2117,2121,2124,2127,2130,2134,2137,2141,2145,2146,2147,2148,2149,2150,2151,2152,2153,2154,2155,2156,2157,2158,2159,2160,2161,2162,2163,2164,2165,2167,2169,2170,2171,2172,2173,2174,2175,2176,2178,2179,2181,2182,2184,2186,2187,2189,2190,2191,2192,2193,2194,2196,2197,2198,2199,2200,2217,2219,2221,2223,2224,2225,2226,2227,2228,2229,2230,2231,2232,2233,2234,2235,2237,2238,2239,2240,2241,2242,2243,2245,2249,2254,2255,2256,2257,2258,2259,2263,2264,2265,2266,2268,2270,2272,2274,2276,2277,2278,2279,2281,2283,2285,2286,2287,2288,2289,2290,2291,2292,2293,2294,2295,2296,2299,2300,2301,2302,2304,2306,2307,2309,2310,2312,2314,2316,2317,2318,2319,2320,2321,2322,2323,2324,2325,2326,2327,2329,2330,2331,2332,2334,2335,2336,2337,2338,2340,2342,2344,2346,2347,2348,2349,2350,2351,2352,2353,2354,2355,2356,2357,2358,2359,2360,2384,2459,2462,2465,2468,2482,2495,2537,2540,2569,2596,2605,2669,3035,3045,3083,3127,3273,3297,3303,3309,3330,3454,3615,3621,3625,3653,3688,3720,3786,3806,3861,3873,3899", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1133,1362,1637,4346,4639,4694,4756,4820,4890,4951,5026,5102,5179,5417,5502,5584,5660,5792,5869,5947,6053,6159,6238,6318,6375,6564,6638,6713,6778,6844,6904,7381,7453,7526,7593,7661,7720,7779,7838,7897,7956,8010,8064,8117,8171,8225,8279,8554,8628,8707,8780,8854,8925,8997,9069,9142,9199,9257,9330,9404,9478,9599,9671,9744,9814,9885,9945,10006,10075,10144,10214,10288,10364,10428,10505,10581,10658,10723,10792,10869,10944,11013,11081,11158,11224,11285,11382,11447,11516,11615,11686,11745,11803,11860,11919,11983,12054,12126,12198,12270,12342,12409,12477,12545,12604,12667,12731,12821,12912,12972,13038,13105,13171,13241,13305,13358,13425,13486,13553,13666,13724,13787,13852,13917,13992,14065,14137,14181,14228,14274,14323,14384,14445,14506,14568,14632,14696,14760,14825,14888,14948,15009,15075,15134,15194,15256,15327,15387,15990,16076,18405,18495,18582,18670,18752,18835,18925,20264,20316,20374,20419,20485,20549,20606,20663,27099,27156,27204,27253,29838,30342,30389,30685,31856,32212,32276,32338,32398,33248,33322,33392,33470,33524,33594,33679,33727,33773,33834,33897,33963,34027,34098,34161,34226,34290,34351,34412,34464,34537,34611,34680,34755,34829,34903,35044,40674,41429,41507,41597,41685,41781,41871,42453,42542,42789,43070,43322,43607,44000,44477,44699,44921,45197,45424,45654,45884,46114,46344,46571,46990,47216,47641,47871,48299,48518,48801,49009,49140,49367,49793,50018,50445,50666,51091,51211,51487,51788,52112,52403,52717,52854,52985,53090,53332,53499,53703,53911,54182,54294,54406,54511,54628,54842,54988,55128,55214,55562,55650,55896,56314,56563,56645,56743,57400,57500,57752,58176,58431,58525,58614,58851,60875,61117,61219,61472,63628,74309,75825,86520,88048,89805,90431,90851,92112,93377,93633,93869,94416,94910,95515,95713,96293,97661,98036,98154,98692,98849,99045,99318,99574,99744,99885,99949,100314,100681,101357,101621,101959,102312,102406,102592,102898,103160,103285,103412,103651,103862,103981,104174,104351,104806,104987,105109,105368,105481,105668,105770,105877,106006,106281,106789,107285,108162,108456,109026,109175,109907,110079,110163,110499,110591,120699,125930,131301,131363,131941,132525,132616,132729,132958,133118,133270,133441,133607,133776,133943,134106,134349,134519,134692,134863,135137,135336,135541,135871,135955,136051,136147,136245,136345,136447,136549,136651,136753,136855,136955,137051,137163,137292,137415,137546,137677,137775,137889,137983,138123,138257,138353,138465,138565,138681,138777,138889,138989,139129,139265,139429,139559,139717,139867,140008,140152,140287,140399,140549,140677,140805,140941,141073,141203,141333,141445,142725,142871,143015,143153,143219,143309,143385,143489,143579,143681,143789,143897,143997,144077,144169,144267,144377,144429,144507,144613,144705,144809,144919,145041,145204,145443,145523,145623,145713,145823,145913,146154,146248,146354,146446,146546,146658,146772,146888,147004,147098,147212,147324,147426,147546,147668,147750,147854,147974,148100,148198,148292,148380,148492,148608,148730,148842,149017,149133,149219,149311,149423,149547,149614,149740,149808,149936,150080,150208,150277,150372,150487,150600,150699,150808,150919,151030,151131,151236,151336,151466,151557,151680,151774,151886,151972,152076,152172,152260,152378,152482,152586,152712,152800,152908,153008,153098,153208,153292,153394,153478,153532,153596,153702,153788,153898,153982,155436,158052,158170,158285,158365,158726,159263,160667,160745,162089,163450,163838,166681,176842,177180,178851,180784,185881,186632,186894,187094,187473,191751,197568,197797,197948,199145,200228,201078,204104,204848,206979,207319,208630", "endLines": "29,33,40,98,104,105,106,107,108,109,110,111,112,115,116,117,118,120,121,122,123,124,125,126,127,130,131,132,133,134,135,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,162,163,164,165,166,167,168,169,170,171,172,173,174,175,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,272,273,312,313,314,315,316,317,318,338,339,340,341,342,343,344,345,429,430,431,432,488,498,499,505,527,534,535,536,537,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,652,666,667,668,669,670,678,679,683,687,691,696,702,709,713,717,722,726,730,734,738,742,746,752,756,762,766,772,776,781,785,788,792,798,802,808,812,818,821,825,829,833,837,841,842,843,844,847,850,853,856,860,861,862,863,864,867,869,871,873,878,879,883,889,893,894,896,908,909,913,919,923,924,925,929,956,960,961,965,993,1165,1191,1362,1388,1419,1427,1433,1449,1471,1476,1481,1491,1500,1509,1513,1520,1539,1546,1547,1556,1559,1562,1566,1570,1574,1577,1578,1583,1588,1598,1603,1610,1616,1617,1620,1624,1629,1631,1633,1636,1639,1641,1645,1648,1655,1658,1661,1665,1667,1671,1673,1675,1677,1681,1689,1697,1709,1715,1724,1727,1738,1741,1742,1747,1748,1753,1998,2068,2069,2079,2088,2089,2091,2095,2098,2101,2104,2107,2110,2113,2116,2120,2123,2126,2129,2133,2136,2140,2144,2145,2146,2147,2148,2149,2150,2151,2152,2153,2154,2155,2156,2157,2158,2159,2160,2161,2162,2163,2164,2166,2168,2169,2170,2171,2172,2173,2174,2175,2177,2178,2180,2181,2183,2185,2186,2188,2189,2190,2191,2192,2193,2195,2196,2197,2198,2199,2200,2218,2220,2222,2223,2224,2225,2226,2227,2228,2229,2230,2231,2232,2233,2234,2236,2237,2238,2239,2240,2241,2242,2244,2248,2252,2254,2255,2256,2257,2258,2262,2263,2264,2265,2267,2269,2271,2273,2275,2276,2277,2278,2280,2282,2284,2285,2286,2287,2288,2289,2290,2291,2292,2293,2294,2295,2298,2299,2300,2301,2303,2305,2306,2308,2309,2311,2313,2315,2316,2317,2318,2319,2320,2321,2322,2323,2324,2325,2326,2328,2329,2330,2331,2333,2334,2335,2336,2337,2339,2341,2343,2345,2346,2347,2348,2349,2350,2351,2352,2353,2354,2355,2356,2357,2358,2359,2360,2458,2461,2464,2467,2481,2487,2504,2539,2568,2595,2604,2668,3031,3038,3072,3110,3144,3296,3302,3308,3329,3453,3473,3620,3624,3630,3687,3699,3785,3805,3860,3872,3898,3905", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "1183,1402,1681,4382,4689,4751,4815,4885,4946,5021,5097,5174,5252,5497,5579,5655,5731,5864,5942,6048,6154,6233,6313,6370,6428,6633,6708,6773,6839,6899,6960,7448,7521,7588,7656,7715,7774,7833,7892,7951,8005,8059,8112,8166,8220,8274,8328,8623,8702,8775,8849,8920,8992,9064,9137,9194,9252,9325,9399,9473,9548,9666,9739,9809,9880,9940,10001,10070,10139,10209,10283,10359,10423,10500,10576,10653,10718,10787,10864,10939,11008,11076,11153,11219,11280,11377,11442,11511,11610,11681,11740,11798,11855,11914,11978,12049,12121,12193,12265,12337,12404,12472,12540,12599,12662,12726,12816,12907,12967,13033,13100,13166,13236,13300,13353,13420,13481,13548,13661,13719,13782,13847,13912,13987,14060,14132,14176,14223,14269,14318,14379,14440,14501,14563,14627,14691,14755,14820,14883,14943,15004,15070,15129,15189,15251,15322,15382,15450,16071,16158,18490,18577,18665,18747,18830,18920,19011,20311,20369,20414,20480,20544,20601,20658,20712,27151,27199,27248,27299,29867,30384,30433,30726,31883,32271,32333,32393,32450,33317,33387,33465,33519,33589,33674,33722,33768,33829,33892,33958,34022,34093,34156,34221,34285,34346,34407,34459,34532,34606,34675,34750,34824,34898,35039,35109,40722,41502,41592,41680,41776,41866,42448,42537,42784,43065,43317,43602,43995,44472,44694,44916,45192,45419,45649,45879,46109,46339,46566,46985,47211,47636,47866,48294,48513,48796,49004,49135,49362,49788,50013,50440,50661,51086,51206,51482,51783,52107,52398,52712,52849,52980,53085,53327,53494,53698,53906,54177,54289,54401,54506,54623,54837,54983,55123,55209,55557,55645,55891,56309,56558,56640,56738,57395,57495,57747,58171,58426,58520,58609,58846,60870,61112,61214,61467,63623,74304,75820,86515,88043,89800,90426,90846,92107,93372,93628,93864,94411,94905,95510,95708,96288,97656,98031,98149,98687,98844,99040,99313,99569,99739,99880,99944,100309,100676,101352,101616,101954,102307,102401,102587,102893,103155,103280,103407,103646,103857,103976,104169,104346,104801,104982,105104,105363,105476,105663,105765,105872,106001,106276,106784,107280,108157,108451,109021,109170,109902,110074,110158,110494,110586,110864,125925,131296,131358,131936,132520,132611,132724,132953,133113,133265,133436,133602,133771,133938,134101,134344,134514,134687,134858,135132,135331,135536,135866,135950,136046,136142,136240,136340,136442,136544,136646,136748,136850,136950,137046,137158,137287,137410,137541,137672,137770,137884,137978,138118,138252,138348,138460,138560,138676,138772,138884,138984,139124,139260,139424,139554,139712,139862,140003,140147,140282,140394,140544,140672,140800,140936,141068,141198,141328,141440,141580,142866,143010,143148,143214,143304,143380,143484,143574,143676,143784,143892,143992,144072,144164,144262,144372,144424,144502,144608,144700,144804,144914,145036,145199,145356,145518,145618,145708,145818,145908,146149,146243,146349,146441,146541,146653,146767,146883,146999,147093,147207,147319,147421,147541,147663,147745,147849,147969,148095,148193,148287,148375,148487,148603,148725,148837,149012,149128,149214,149306,149418,149542,149609,149735,149803,149931,150075,150203,150272,150367,150482,150595,150694,150803,150914,151025,151126,151231,151331,151461,151552,151675,151769,151881,151967,152071,152167,152255,152373,152477,152581,152707,152795,152903,153003,153093,153203,153287,153389,153473,153527,153591,153697,153783,153893,153977,154097,158047,158165,158280,158360,158721,158954,159775,160740,162084,163445,163833,166676,176729,176972,178545,180203,181351,186627,186889,187089,187468,191746,192352,197792,197943,198158,200223,200535,204099,204843,206974,207314,208625,208828"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\c7360db8db21da0cd2a2513de2c76e03\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "528", "startColumns": "4", "startOffsets": "31888", "endColumns": "42", "endOffsets": "31926"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\4c124e34a7f4172b432f9fe272bec823\\transformed\\activity-1.9.3\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "500,529", "startColumns": "4,4", "startOffsets": "30438,31931", "endColumns": "41,59", "endOffsets": "30475,31986"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\018c63b1485334add67611329c38a7ad\\transformed\\appcompat-resources-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2505,2521,2527,3700,3716", "startColumns": "4,4,4,4,4", "startOffsets": "159780,160205,160383,200540,200951", "endLines": "2520,2526,2536,3715,3719", "endColumns": "24,24,24,24,24", "endOffsets": "160200,160378,160662,200946,201073"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\1a24962cf41916db9572a74a797a0e06\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "530", "startColumns": "4", "startOffsets": "31991", "endColumns": "53", "endOffsets": "32040"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\3e8e26f56d8e20e616f6e0fb0878dba9\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "490,497", "startColumns": "4,4", "startOffsets": "29927,30275", "endColumns": "53,66", "endOffsets": "29976,30337"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\f0ce41bc1ab5a7ccfdd795c249b2c5b2\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,435,437,438,487,489,533,585,586,587,588,589,643,644,646,649,650,651,653,654,655,656,658,659,660,1754,1923,1926", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "25189,25248,25307,25367,25427,25487,25547,25607,25667,25727,25787,25847,25907,25966,26026,26086,26146,26206,26266,26326,26386,26446,26506,26566,26625,26685,26745,26804,26863,26922,26981,27040,27385,27509,27567,29787,29872,32159,35750,35815,35869,35935,36036,40218,40270,40369,40516,40570,40620,40727,40773,40819,40861,40972,41019,41055,110869,120393,120504", "endLines": "397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,435,437,438,487,489,533,585,586,587,588,589,643,644,646,649,650,651,653,654,655,656,658,659,660,1756,1925,1929", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "25243,25302,25362,25422,25482,25542,25602,25662,25722,25782,25842,25902,25961,26021,26081,26141,26201,26261,26321,26381,26441,26501,26561,26620,26680,26740,26799,26858,26917,26976,27035,27094,27454,27562,27617,29833,29922,32207,35810,35864,35930,36031,36089,40265,40325,40426,40565,40615,40669,40768,40814,40856,40896,41014,41050,41140,110976,120499,120694"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\9db5af2f2458547656275ea3d5385dab\\transformed\\media3-exoplayer-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,632", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "120,182,247,311,388,453,543,627,696"}, "to": {"startLines": "616,617,618,619,620,621,622,623,624", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "38226,38296,38358,38423,38487,38564,38629,38719,38803", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "38291,38353,38418,38482,38559,38624,38714,38798,38867"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\02a2dc3fb6f2f628d8f50a1ef0801c78\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "576", "startColumns": "4", "startOffsets": "35114", "endColumns": "82", "endOffsets": "35192"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\e3284905063e776d62ae70595f5b34b4\\transformed\\fragment-1.5.4\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "482,504,532,3199,3204", "startColumns": "4,4,4,4,4", "startOffsets": "29540,30620,32095,183768,183938", "endLines": "482,504,532,3203,3207", "endColumns": "56,64,63,24,24", "endOffsets": "29592,30680,32154,183933,184082"}}]}]}