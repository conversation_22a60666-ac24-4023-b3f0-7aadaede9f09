package `is`.xyz.mpv

import android.content.Context
import android.util.AttributeSet

/**
 * Lightweight wrapper that exposes the required constructor and delegates to BaseMPVView.
 * The upstream mpv-android library ships [BaseMPVView]; we provide [MPVView] here so
 * XML inflation with tag `is.xyz.mpv.MPVView` works and clients can call initialize().
 */
class MPVView(
    context: Context,
    attrs: AttributeSet,
) : BaseMPVView(context, attrs) {



    override fun initOptions() {
        MPVLib.setOptionString("rtsp-transport", "udp")
    }

    override fun postInitOptions() {
        TODO("Not yet implemented")
    }

    override fun observeProperties() {
        TODO("Not yet implemented")
    }
}


