package top.yogiczy.mytv.tv.ui.screensold.videoplayer.player

import android.content.Context
import android.view.LayoutInflater
import android.view.SurfaceView
import android.view.TextureView
import android.view.View
import `is`.xyz.mpv.MPVLib
import `is`.xyz.mpv.MPVView
import `is`.xyz.mpv.Utils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import top.yogiczy.mytv.core.data.entities.channel.ChannelLine
import top.yogiczy.mytv.tv.R
import top.yogiczy.mytv.tv.ui.utils.Configs

/**
 * MPV-based player using MPVLib and MPVView (inflated via XML).
 */
class MpvVideoPlayer(
    private val context: Context,
    private val coroutineScope: CoroutineScope,
) : VideoPlayer(coroutineScope) {

    private var mpvView: View? = null
    private var updateJob: Job? = null
    private var lastVolume: Float = 1f
    private var initialized: Boolean = false

    override fun initialize() {
        super.initialize()
    }

    override fun release() {
        updateJob?.cancel()
        updateJob = null
        super.release()
    }

    override fun prepare(line: ChannelLine) {
        runCatching {
            val view = ensureMpvView()
            if (!initialized) {
                // copy assets and init mpv view (method initialize(files, cache))
				Utils.copyAssets(context)
				(view as? MPVView)?.initialize(context.filesDir.path, context.cacheDir.path)
				initialized = true
            }

            // Prepare playback
            MPVLib.command("set", "user-agent", line.httpUserAgent ?: Configs.videoPlayerUserAgent)
            MPVLib.command("set", "rtsp-transport", "udp")
            MPVLib.command("loadfile", line.playableUrl)

            triggerPrepared()

            updateJob?.cancel()
            // updateJob = coroutineScope.launch {
            //     while (true) {
            //         runCatching {
            //             // duration and current position (seconds -> ms)
            //             MPVLib.getPropertyDouble("duration")?.let { triggerDuration((it * 1000).toLong()) }
            //             MPVLib.getPropertyDouble("time-pos")?.let { triggerCurrentPosition((it * 1000).toLong()) }
            //             // resolution
            //             val w = MPVLib.getPropertyInt("video-params/w")
            //             val h = MPVLib.getPropertyInt("video-params/h")
            //             if (w != null && h != null && w > 0 && h > 0) triggerResolution(w, h)
            //         }
            //         delay(500)
            //     }
            // }
        }.onFailure {
            triggerError(PlaybackException("MPV_ERROR_NOT_AVAILABLE", 20001))
        }
    }

    override fun play() {
        runCatching { MPVLib.command("set", "pause", "no") }
    }

    override fun pause() {
        runCatching { MPVLib.command("set", "pause", "yes") }
    }

    override fun seekTo(position: Long) {
        val seconds = (position / 1000.0).toString()
        runCatching { MPVLib.command("seek", seconds, "absolute", "exact") }
    }

    override fun setVolume(volume: Float) {
        lastVolume = volume
        val vol100 = (volume * 100).coerceIn(0f, 100f).toString()
        runCatching { MPVLib.command("set", "volume", vol100) }
    }

    override fun getVolume(): Float = lastVolume

    override fun stop() {
        runCatching { MPVLib.command("stop") }
        updateJob?.cancel()
        super.stop()
    }

    override fun selectVideoTrack(track: Metadata.Video?) {}

    override fun selectAudioTrack(track: Metadata.Audio?) {}

    override fun selectSubtitleTrack(track: Metadata.Subtitle?) {}

    override fun setVideoSurfaceView(surfaceView: SurfaceView) {
        // MPV renders via its own view
    }

    override fun setVideoTextureView(textureView: TextureView) {
        // MPV renders via its own view
    }

    override fun buildCustomView(context: Context): View? = ensureMpvView()

    private fun ensureMpvView(): View {
        mpvView?.let { return it }
        val view = LayoutInflater.from(context).inflate(R.layout.view_mpv, null, false)
        mpvView = view
        return view
    }
}

